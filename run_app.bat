@echo off
echo ========================================
echo    نظام إدارة فواتير الكهرباء
echo ========================================
echo.
echo جاري تشغيل التطبيق...
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM تشغيل التطبيق
python main.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل التطبيق
    echo يرجى التحقق من تثبيت المتطلبات:
    echo pip install -r requirements.txt
    echo.
    pause
)
