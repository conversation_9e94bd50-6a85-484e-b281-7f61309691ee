========================================
نظام إدارة فواتير الكهرباء - تعليمات التشغيل
========================================

مرحباً بك في نظام إدارة فواتير الكهرباء الشامل!

========================================
طرق تشغيل البرنامج:
========================================

الطريقة الأولى: تشغيل مباشر (إذا كان Python مثبت)
1. انقر نقراً مزدوجاً على ملف "run_app.bat"
2. أو افتح موجه الأوامر واكتب: python main.py

الطريقة الثانية: الملف التنفيذي
1. انقر نقراً مزدوجاً على ملف "create_exe.bat" لإنشاء الملف التنفيذي
2. بعد الانتهاء، ستجد الملف في مجلد "dist"
3. انقر نقراً مزدوجاً على "ElectricityBills.exe"

========================================
تثبيت المتطلبات (إذا لزم الأمر):
========================================

1. انقر نقراً مزدوجاً على ملف "install_requirements.bat"
2. انتظر حتى انتهاء التثبيت
3. أعد تشغيل البرنامج

========================================
الوظائف الرئيسية:
========================================

🔍 البحث المتقدم:
- بحث بالاسم، رقم الحساب، رقم المقياس
- بحث بفترة زمنية محددة
- ترتيب النتائج تصاعدياً وتنازلياً
- عرض التفاصيل الكاملة لكل حساب

⚙️ إدارة المعاملات:
- إضافة حسابات جديدة
- تعديل بيانات الحسابات الموجودة
- حذف الحسابات
- بحث سريع في القائمة

📊 التقارير:
- تقرير المبالغ العالية
- تقرير الحسابات الجديدة
- تقرير جميع الحسابات
- تصدير بصيغ Excel, PDF, CSV

🎨 الثيمات:
- اختيار من 8 ثيمات مختلفة
- واجهة عربية احترافية
- ألوان متناسقة وجميلة

========================================
البيانات المدعومة:
========================================

الحقول الأساسية:
- رقم الحساب (ACCTNO) *مطلوب
- الاسم (NAME_A) *مطلوب
- رقم المقياس (MATER_NO)
- العنوان (ADRESS)
- المستحق (OUTS)
- المتأخر (BKOUTS)
- تاريخ الفاتورة (BILL_DATE)

الحقول الإضافية:
- رقم التركيب، الرقم التسلسلي
- رقم المنزل، نوع المقياس
- معامل المقياس، القراءات
- الإيجارات والرسوم
- تواريخ الدفع والقراءات
- العلامات والأكواد

========================================
نصائح للاستخدام:
========================================

1. البحث:
   - استخدم البحث المتقدم للبحث المفصل
   - استخدم البحث السريع في إدارة المعاملات
   - انقر نقراً مزدوجاً على أي نتيجة لعرض التفاصيل

2. إضافة البيانات:
   - الحقول المميزة بـ * مطلوبة
   - استخدم تنسيق التاريخ: YYYY-MM-DD أو DD/MM/YYYY
   - تأكد من صحة الأرقام قبل الحفظ

3. التقارير:
   - حدد المعايير المناسبة قبل إنشاء التقرير
   - اختر صيغة التصدير المناسبة
   - احفظ التقارير في مكان آمن

4. النسخ الاحتياطي:
   - انسخ ملف "electricity_bills.db" بانتظام
   - احتفظ بنسخة احتياطية في مكان آمن

========================================
حل المشاكل الشائعة:
========================================

مشكلة: البرنامج لا يعمل
الحل: تأكد من تثبيت Python وتشغيل install_requirements.bat

مشكلة: خطأ في قاعدة البيانات
الحل: احذف ملف electricity_bills.db وأعد تشغيل البرنامج

مشكلة: لا يمكن تصدير PDF
الحل: تأكد من تثبيت reportlab: pip install reportlab

مشكلة: لا يمكن تصدير Excel
الحل: تأكد من تثبيت openpyxl: pip install openpyxl

========================================
الدعم الفني:
========================================

للحصول على المساعدة:
1. راجع ملف README.md
2. تحقق من ملف test_database.py لاختبار النظام
3. تأكد من تحديث جميع المتطلبات

========================================
معلومات إضافية:
========================================

- النظام يدعم اللغة العربية بالكامل
- قاعدة البيانات محلية وآمنة
- جميع المعاملات مسجلة ومؤرخة
- يمكن تشغيل النظام بدون اتصال بالإنترنت

========================================
شكراً لاستخدام نظام إدارة فواتير الكهرباء!
========================================
