(['C:\\py\\main.py'],
 ['C:\\py'],
 [],
 [('C:\\python313\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('C:\\python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('build_exe.py', 'C:\\py\\build_exe.py', 'DATA'),
  ('config.py', 'C:\\py\\config.py', 'DATA'),
  ('database.py', 'C:\\py\\database.py', 'DATA'),
  ('main.py', 'C:\\py\\main.py', 'DATA'),
  ('reports.py', 'C:\\py\\reports.py', 'DATA'),
  ('search_module.py', 'C:\\py\\search_module.py', 'DATA'),
  ('test_database.py', 'C:\\py\\test_database.py', 'DATA'),
  ('themes.py', 'C:\\py\\themes.py', 'DATA'),
  ('transactions.py', 'C:\\py\\transactions.py', 'DATA'),
  ('ui_components.py', 'C:\\py\\ui_components.py', 'DATA')],
 '3.13.4 (tags/v3.13.4:8a526ec, Jun  3 2025, 17:46:04) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'C:\\py\\main.py', 'PYSOURCE')],
 [('_distutils_hack',
   'C:\\python313\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('threading', 'C:\\python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('contextlib', 'C:\\python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('_strptime', 'C:\\python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'C:\\python313\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'C:\\python313\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'C:\\python313\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'C:\\python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib', 'C:\\python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'C:\\python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('glob', 'C:\\python313\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('py_compile', 'C:\\python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('struct', 'C:\\python313\\Lib\\struct.py', 'PYMODULE'),
  ('tarfile', 'C:\\python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\python313\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\python313\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\python313\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\python313\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'C:\\python313\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\python313\\Lib\\gettext.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'C:\\python313\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'C:\\python313\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('random', 'C:\\python313\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\python313\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\python313\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\python313\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'C:\\python313\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\python313\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\python313\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'C:\\python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'C:\\python313\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\python313\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('quopri', 'C:\\python313\\Lib\\quopri.py', 'PYMODULE'),
  ('typing', 'C:\\python313\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('inspect', 'C:\\python313\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'C:\\python313\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'C:\\python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'C:\\python313\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'C:\\python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'C:\\python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'C:\\python313\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'C:\\python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'C:\\python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'C:\\python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\python313\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('subprocess', 'C:\\python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\python313\\Lib\\signal.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\python313\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('pkgutil', 'C:\\python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('asyncio', 'C:\\python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'C:\\python313\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'C:\\python313\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\python313\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'C:\\python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'C:\\python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'C:\\python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\python313\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\python313\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib.response', 'C:\\python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'C:\\python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'C:\\python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'C:\\python313\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'C:\\python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'C:\\python313\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'C:\\python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'C:\\python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'C:\\python313\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'C:\\python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'C:\\python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'C:\\python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'C:\\python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\python313\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'C:\\python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'C:\\python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'C:\\python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\python313\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'C:\\python313\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\python313\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\python313\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\python313\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\python313\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\python313\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\python313\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\python313\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\python313\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\python313\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\python313\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\python313\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\python313\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\python313\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\python313\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\python313\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\python313\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\python313\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\python313\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\python313\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\python313\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\python313\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\python313\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\python313\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\python313\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\python313\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\python313\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'C:\\python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\python313\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\python313\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\python313\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\python313\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\python313\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\python313\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\python313\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\python313\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\python313\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\python313\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('datetime', 'C:\\python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('transactions', 'C:\\py\\transactions.py', 'PYMODULE'),
  ('reports', 'C:\\py\\reports.py', 'PYMODULE'),
  ('openpyxl.styles',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\python313\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\python313\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\python313\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\python313\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\python313\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\python313\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\python313\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\python313\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\python313\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\python313\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\python313\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\python313\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\python313\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\python313\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\python313\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'C:\\python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\python313\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\python313\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\python313\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\python313\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\python313\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'C:\\python313\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\python313\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'C:\\python313\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\python313\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\python313\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\python313\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\python313\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\python313\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\python313\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\python313\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\python313\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\python313\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\python313\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\python313\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\python313\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\python313\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\python313\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\python313\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\python313\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\python313\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\python313\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\python313\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\python313\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\python313\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\python313\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\python313\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\python313\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\python313\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\python313\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\python313\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('PIL.Image', 'C:\\python313\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\python313\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\python313\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\python313\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\python313\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\python313\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\python313\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\python313\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\python313\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._util', 'C:\\python313\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._typing',
   'C:\\python313\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\python313\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\python313\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\python313\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\python313\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\python313\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL', 'C:\\python313\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL._version',
   'C:\\python313\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\python313\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('reportlab.pdfbase.ttfonts',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\ttfonts.py',
   'PYMODULE'),
  ('reportlab.lib.rl_accel',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\rl_accel.py',
   'PYMODULE'),
  ('reportlab.rl_config',
   'C:\\python313\\Lib\\site-packages\\reportlab\\rl_config.py',
   'PYMODULE'),
  ('reportlab',
   'C:\\python313\\Lib\\site-packages\\reportlab\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfdoc',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\pdfdoc.py',
   'PYMODULE'),
  ('reportlab.lib.rparsexml',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\rparsexml.py',
   'PYMODULE'),
  ('chardet',
   'C:\\python313\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\python313\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\python313\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\python313\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\python313\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\python313\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\python313\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\python313\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\python313\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\python313\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\python313\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\python313\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\python313\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\python313\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\python313\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\python313\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\python313\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\python313\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\python313\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\python313\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_zapfdingbats',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_symbol',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbolditalic',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbolditalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesitalic',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesitalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbold',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesroman',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaboldoblique',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaoblique',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticabold',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticabold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helvetica',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helvetica.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierboldoblique',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courieroblique',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courieroblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierbold',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courier',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courier.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macexpert',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macexpert.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_pdfdoc',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_pdfdoc.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_zapfdingbats',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_symbol',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_standard',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_standard.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macroman',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_winansi',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_winansi.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfutils',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\pdfutils.py',
   'PYMODULE'),
  ('reportlab.lib.abag',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\abag.py',
   'PYMODULE'),
  ('reportlab.lib.utils',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\utils.py',
   'PYMODULE'),
  ('reportlab.rl_settings',
   'C:\\python313\\Lib\\site-packages\\reportlab\\rl_settings.py',
   'PYMODULE'),
  ('reportlab.lib.rl_safe_eval',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\rl_safe_eval.py',
   'PYMODULE'),
  ('reportlab.lib.rltempfile',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\rltempfile.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfmetrics',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\pdfmetrics.py',
   'PYMODULE'),
  ('reportlab.lib.fonts',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\fonts.py',
   'PYMODULE'),
  ('reportlab.pdfbase._glyphlist',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\_glyphlist.py',
   'PYMODULE'),
  ('reportlab.pdfbase.rl_codecs',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\rl_codecs.py',
   'PYMODULE'),
  ('reportlab.lib.logger',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\logger.py',
   'PYMODULE'),
  ('reportlab.pdfbase',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib.colors',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\colors.py',
   'PYMODULE'),
  ('reportlab.lib',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib.PyFontify',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\PyFontify.py',
   'PYMODULE'),
  ('reportlab.lib.pdfencrypt',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\pdfencrypt.py',
   'PYMODULE'),
  ('reportlab.lib.arciv',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\arciv.py',
   'PYMODULE'),
  ('reportlab.platypus.flowables',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\flowables.py',
   'PYMODULE'),
  ('reportlab.lib.sequencer',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\sequencer.py',
   'PYMODULE'),
  ('reportlab.platypus.frames',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\frames.py',
   'PYMODULE'),
  ('reportlab.platypus.paragraph',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\paragraph.py',
   'PYMODULE'),
  ('reportlab.lib.units',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\units.py',
   'PYMODULE'),
  ('reportlab.lib.geomutils',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\geomutils.py',
   'PYMODULE'),
  ('reportlab.platypus.paraparser',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\paraparser.py',
   'PYMODULE'),
  ('html.parser', 'C:\\python313\\Lib\\html\\parser.py', 'PYMODULE'),
  ('_markupbase', 'C:\\python313\\Lib\\_markupbase.py', 'PYMODULE'),
  ('reportlab.pdfgen.textobject',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\textobject.py',
   'PYMODULE'),
  ('reportlab.pdfgen',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pathobject',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\pathobject.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfgeom',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\pdfgeom.py',
   'PYMODULE'),
  ('reportlab.platypus.doctemplate',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\doctemplate.py',
   'PYMODULE'),
  ('reportlab.lib.randomtext',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\randomtext.py',
   'PYMODULE'),
  ('reportlab.lib.enums',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\enums.py',
   'PYMODULE'),
  ('reportlab.pdfgen.canvas',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\canvas.py',
   'PYMODULE'),
  ('reportlab.pdfbase.acroform',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfbase\\acroform.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfimages',
   'C:\\python313\\Lib\\site-packages\\reportlab\\pdfgen\\pdfimages.py',
   'PYMODULE'),
  ('reportlab.lib.boxstuff',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\boxstuff.py',
   'PYMODULE'),
  ('reportlab.lib.textsplit',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\textsplit.py',
   'PYMODULE'),
  ('reportlab.lib.styles',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\styles.py',
   'PYMODULE'),
  ('reportlab.platypus',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\__init__.py',
   'PYMODULE'),
  ('reportlab.platypus.xpreformatted',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\xpreformatted.py',
   'PYMODULE'),
  ('reportlab.platypus.tables',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\tables.py',
   'PYMODULE'),
  ('reportlab.lib.validators',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\validators.py',
   'PYMODULE'),
  ('reportlab.lib.normalDate',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\normalDate.py',
   'PYMODULE'),
  ('reportlab.graphics.shapes',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\shapes.py',
   'PYMODULE'),
  ('reportlab.graphics.renderSVG',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\renderSVG.py',
   'PYMODULE'),
  ('reportlab.lib.corp',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\corp.py',
   'PYMODULE'),
  ('reportlab.graphics.widgetbase',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\widgetbase.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.piecharts',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\piecharts.py',
   'PYMODULE'),
  ('reportlab.graphics.charts',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils3d',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils3d.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.textlabels',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\textlabels.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.utils',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.legends',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\legends.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.areas',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\charts\\areas.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.markers',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\widgets\\markers.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\widgets\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.flags',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\widgets\\flags.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.signsandsymbols',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\widgets\\signsandsymbols.py',
   'PYMODULE'),
  ('reportlab.lib.formatters',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\formatters.py',
   'PYMODULE'),
  ('reportlab.graphics.testshapes',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\testshapes.py',
   'PYMODULE'),
  ('xml.dom', 'C:\\python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.minidom', 'C:\\python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('reportlab.graphics.renderbase',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\renderbase.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPS',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\renderPS.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPM',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\renderPM.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPDF',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\renderPDF.py',
   'PYMODULE'),
  ('reportlab.graphics',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.transform',
   'C:\\python313\\Lib\\site-packages\\reportlab\\graphics\\transform.py',
   'PYMODULE'),
  ('reportlab.lib.attrmap',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\attrmap.py',
   'PYMODULE'),
  ('reportlab.platypus.multicol',
   'C:\\python313\\Lib\\site-packages\\reportlab\\platypus\\multicol.py',
   'PYMODULE'),
  ('reportlab.lib.pagesizes',
   'C:\\python313\\Lib\\site-packages\\reportlab\\lib\\pagesizes.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog', 'C:\\python313\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('search_module', 'C:\\py\\search_module.py', 'PYMODULE'),
  ('ui_components', 'C:\\py\\ui_components.py', 'PYMODULE'),
  ('themes', 'C:\\py\\themes.py', 'PYMODULE'),
  ('database', 'C:\\py\\database.py', 'PYMODULE'),
  ('sqlite3', 'C:\\python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('config', 'C:\\py\\config.py', 'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\python313\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\python313\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\python313\\Lib\\tkinter\\constants.py',
   'PYMODULE')],
 [('python313.dll', 'C:\\python313\\python313.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\python313\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\python313\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('unicodedata.pyd', 'C:\\python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'C:\\python313\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\python313\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'C:\\python313\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\python313\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\python313\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\python313\\DLLs\\tcl86t.dll', 'BINARY'),
  ('zlib1.dll', 'C:\\python313\\DLLs\\zlib1.dll', 'BINARY')],
 [],
 [],
 [('build_exe.py', 'C:\\py\\build_exe.py', 'DATA'),
  ('config.py', 'C:\\py\\config.py', 'DATA'),
  ('database.py', 'C:\\py\\database.py', 'DATA'),
  ('main.py', 'C:\\py\\main.py', 'DATA'),
  ('reports.py', 'C:\\py\\reports.py', 'DATA'),
  ('search_module.py', 'C:\\py\\search_module.py', 'DATA'),
  ('test_database.py', 'C:\\py\\test_database.py', 'DATA'),
  ('themes.py', 'C:\\py\\themes.py', 'DATA'),
  ('transactions.py', 'C:\\py\\transactions.py', 'DATA'),
  ('ui_components.py', 'C:\\py\\ui_components.py', 'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'C:\\python313\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'C:\\python313\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tm.tcl', 'C:\\python313\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tk_data\\iconlist.tcl', 'C:\\python313\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'C:\\python313\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\python313\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\python313\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'C:\\python313\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\python313\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\init.tcl', 'C:\\python313\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\entry.tcl', 'C:\\python313\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\python313\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tclIndex', 'C:\\python313\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\python313\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\python313\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'C:\\python313\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\python313\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tk_data\\tk.tcl', 'C:\\python313\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tk_data\\tclIndex', 'C:\\python313\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'C:\\python313\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\python313\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\python313\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'C:\\python313\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'C:\\python313\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\python313\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\python313\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'C:\\python313\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'C:\\python313\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'C:\\python313\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'C:\\python313\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\python313\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\scale.tcl', 'C:\\python313\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'C:\\python313\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tk_data\\text.tcl', 'C:\\python313\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'C:\\python313\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\python313\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'C:\\python313\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\package.tcl', 'C:\\python313\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\python313\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\word.tcl', 'C:\\python313\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\history.tcl', 'C:\\python313\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\python313\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tk_data\\console.tcl', 'C:\\python313\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'C:\\python313\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'C:\\python313\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'C:\\python313\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\python313\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tk_data\\menu.tcl', 'C:\\python313\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'C:\\python313\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\icons.tcl', 'C:\\python313\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'C:\\python313\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tk_data\\palette.tcl', 'C:\\python313\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\python313\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tk_data\\focus.tcl', 'C:\\python313\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'C:\\python313\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'C:\\python313\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'C:\\python313\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\python313\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\python313\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\python313\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tk_data\\button.tcl', 'C:\\python313\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\WET', 'C:\\python313\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\python313\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\python313\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\python313\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('numpy-2.3.0.dist-info\\entry_points.txt',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\DELVEWHEEL',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\METADATA',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.0.dist-info\\LICENSE.txt',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\RECORD',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info\\INSTALLER',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.0.dist-info\\WHEEL',
   'C:\\python313\\Lib\\site-packages\\numpy-2.3.0.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'C:\\py\\build\\ElectricityBills\\base_library.zip',
   'DATA')],
 [('abc', 'C:\\python313\\Lib\\abc.py', 'PYMODULE'),
  ('types', 'C:\\python313\\Lib\\types.py', 'PYMODULE'),
  ('ntpath', 'C:\\python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('weakref', 'C:\\python313\\Lib\\weakref.py', 'PYMODULE'),
  ('functools', 'C:\\python313\\Lib\\functools.py', 'PYMODULE'),
  ('sre_compile', 'C:\\python313\\Lib\\sre_compile.py', 'PYMODULE'),
  ('linecache', 'C:\\python313\\Lib\\linecache.py', 'PYMODULE'),
  ('collections', 'C:\\python313\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('stat', 'C:\\python313\\Lib\\stat.py', 'PYMODULE'),
  ('heapq', 'C:\\python313\\Lib\\heapq.py', 'PYMODULE'),
  ('genericpath', 'C:\\python313\\Lib\\genericpath.py', 'PYMODULE'),
  ('re._parser', 'C:\\python313\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'C:\\python313\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'C:\\python313\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'C:\\python313\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'C:\\python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'C:\\python313\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'C:\\python313\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'C:\\python313\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'C:\\python313\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'C:\\python313\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'C:\\python313\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'C:\\python313\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'C:\\python313\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'C:\\python313\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'C:\\python313\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'C:\\python313\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'C:\\python313\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'C:\\python313\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'C:\\python313\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'C:\\python313\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\python313\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'C:\\python313\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'C:\\python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'C:\\python313\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'C:\\python313\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'C:\\python313\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'C:\\python313\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'C:\\python313\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'C:\\python313\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'C:\\python313\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'C:\\python313\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'C:\\python313\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'C:\\python313\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'C:\\python313\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'C:\\python313\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'C:\\python313\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'C:\\python313\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'C:\\python313\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'C:\\python313\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'C:\\python313\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'C:\\python313\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'C:\\python313\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'C:\\python313\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'C:\\python313\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'C:\\python313\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'C:\\python313\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'C:\\python313\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'C:\\python313\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'C:\\python313\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'C:\\python313\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'C:\\python313\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'C:\\python313\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'C:\\python313\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'C:\\python313\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'C:\\python313\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'C:\\python313\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'C:\\python313\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'C:\\python313\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'C:\\python313\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'C:\\python313\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'C:\\python313\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'C:\\python313\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'C:\\python313\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'C:\\python313\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'C:\\python313\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'C:\\python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'C:\\python313\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'C:\\python313\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'C:\\python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'C:\\python313\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('traceback', 'C:\\python313\\Lib\\traceback.py', 'PYMODULE'),
  ('copyreg', 'C:\\python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('io', 'C:\\python313\\Lib\\io.py', 'PYMODULE'),
  ('locale', 'C:\\python313\\Lib\\locale.py', 'PYMODULE'),
  ('sre_parse', 'C:\\python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('codecs', 'C:\\python313\\Lib\\codecs.py', 'PYMODULE'),
  ('operator', 'C:\\python313\\Lib\\operator.py', 'PYMODULE'),
  ('enum', 'C:\\python313\\Lib\\enum.py', 'PYMODULE'),
  ('_collections_abc', 'C:\\python313\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('sre_constants', 'C:\\python313\\Lib\\sre_constants.py', 'PYMODULE'),
  ('_weakrefset', 'C:\\python313\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('reprlib', 'C:\\python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('posixpath', 'C:\\python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('keyword', 'C:\\python313\\Lib\\keyword.py', 'PYMODULE'),
  ('warnings', 'C:\\python313\\Lib\\warnings.py', 'PYMODULE'),
  ('os', 'C:\\python313\\Lib\\os.py', 'PYMODULE')])
