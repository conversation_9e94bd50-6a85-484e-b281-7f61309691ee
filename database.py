# -*- coding: utf-8 -*-
"""
وحدة إدارة قاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime
from config import DATABASE_PATH

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إنشاء جدول الحسابات الرئيسي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ACCTNO TEXT UNIQUE NOT NULL,
                INSTALL_NO TEXT,
                SERIAL TEXT,
                NAME_A TEXT NOT NULL,
                HOUSE_NO TEXT,
                ADRESS TEXT,
                MATER_NO TEXT,
                MPHASE TEXT,
                METER_FACT REAL,
                LAST_READ REAL,
                LAST_DATE TEXT,
                PREV_READ REAL,
                PREV_DATE TEXT,
                METER_RENT REAL,
                CB_RENT REAL,
                OTHCHARGE REAL,
                OUTS REAL,
                BKOUTS REAL,
                HOUSE_COD TEXT,
                EVEN_CLOSE TEXT,
                PAYMENT REAL,
                PAY_DATE TEXT,
                BILL_DATE TEXT,
                OLD_EXCH TEXT,
                BAD_M_FLAG TEXT,
                SPERE_FLAG TEXT,
                BILL_CAL REAL,
                AV_CONS REAL,
                OPR_FLG TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول سجل المعاملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER,
                transaction_type TEXT NOT NULL,
                description TEXT,
                old_values TEXT,
                new_values TEXT,
                user_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')
        
        # إنشاء فهارس للبحث السريع
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_acctno ON accounts(ACCTNO)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_name ON accounts(NAME_A)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_meter ON accounts(MATER_NO)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_bill_date ON accounts(BILL_DATE)')
        
        conn.commit()
        conn.close()
    
    def add_account(self, account_data):
        """إضافة حساب جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            columns = ', '.join(account_data.keys())
            placeholders = ', '.join(['?' for _ in account_data])
            values = list(account_data.values())
            
            cursor.execute(f'''
                INSERT INTO accounts ({columns})
                VALUES ({placeholders})
            ''', values)
            
            account_id = cursor.lastrowid
            
            # تسجيل المعاملة
            self.log_transaction(cursor, account_id, 'ADD', 'إضافة حساب جديد', '', str(account_data))
            
            conn.commit()
            return True, account_id
            
        except sqlite3.IntegrityError as e:
            return False, f"خطأ: رقم الحساب موجود مسبقاً - {str(e)}"
        except Exception as e:
            return False, f"خطأ في إضافة الحساب: {str(e)}"
        finally:
            conn.close()
    
    def update_account(self, account_id, account_data):
        """تحديث بيانات حساب"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # الحصول على البيانات القديمة
            cursor.execute('SELECT * FROM accounts WHERE id = ?', (account_id,))
            old_data = cursor.fetchone()
            
            if not old_data:
                return False, "الحساب غير موجود"
            
            # تحديث البيانات
            set_clause = ', '.join([f'{key} = ?' for key in account_data.keys()])
            values = list(account_data.values()) + [account_id]
            
            cursor.execute(f'''
                UPDATE accounts 
                SET {set_clause}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', values)
            
            # تسجيل المعاملة
            self.log_transaction(cursor, account_id, 'UPDATE', 'تحديث بيانات الحساب', 
                               str(old_data), str(account_data))
            
            conn.commit()
            return True, "تم التحديث بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تحديث الحساب: {str(e)}"
        finally:
            conn.close()
    
    def delete_account(self, account_id):
        """حذف حساب"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # الحصول على بيانات الحساب قبل الحذف
            cursor.execute('SELECT * FROM accounts WHERE id = ?', (account_id,))
            account_data = cursor.fetchone()
            
            if not account_data:
                return False, "الحساب غير موجود"
            
            # حذف الحساب
            cursor.execute('DELETE FROM accounts WHERE id = ?', (account_id,))
            
            # تسجيل المعاملة
            self.log_transaction(cursor, account_id, 'DELETE', 'حذف الحساب', 
                               str(account_data), '')
            
            conn.commit()
            return True, "تم الحذف بنجاح"
            
        except Exception as e:
            return False, f"خطأ في حذف الحساب: {str(e)}"
        finally:
            conn.close()
    
    def log_transaction(self, cursor, account_id, transaction_type, description, old_values, new_values):
        """تسجيل المعاملة في سجل المعاملات"""
        cursor.execute('''
            INSERT INTO transactions_log 
            (account_id, transaction_type, description, old_values, new_values, user_name)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (account_id, transaction_type, description, old_values, new_values, 'System'))
    
    def search_accounts(self, search_criteria, sort_by='NAME_A', sort_order='ASC'):
        """البحث في الحسابات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            where_conditions = []
            params = []
            
            # بناء شروط البحث
            if search_criteria.get('name'):
                where_conditions.append('NAME_A LIKE ?')
                params.append(f"%{search_criteria['name']}%")
            
            if search_criteria.get('account_no'):
                where_conditions.append('ACCTNO LIKE ?')
                params.append(f"%{search_criteria['account_no']}%")
            
            if search_criteria.get('meter_no'):
                where_conditions.append('MATER_NO LIKE ?')
                params.append(f"%{search_criteria['meter_no']}%")
            
            if search_criteria.get('date_from'):
                where_conditions.append('BILL_DATE >= ?')
                params.append(search_criteria['date_from'])
            
            if search_criteria.get('date_to'):
                where_conditions.append('BILL_DATE <= ?')
                params.append(search_criteria['date_to'])
            
            # بناء الاستعلام
            query = 'SELECT * FROM accounts'
            if where_conditions:
                query += ' WHERE ' + ' AND '.join(where_conditions)
            
            query += f' ORDER BY {sort_by} {sort_order}'
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # الحصول على أسماء الأعمدة
            columns = [description[0] for description in cursor.description]
            
            return True, results, columns
            
        except Exception as e:
            return False, f"خطأ في البحث: {str(e)}", []
        finally:
            conn.close()
    
    def get_all_accounts(self, sort_by='NAME_A', sort_order='ASC'):
        """الحصول على جميع الحسابات"""
        return self.search_accounts({}, sort_by, sort_order)
    
    def get_account_by_id(self, account_id):
        """الحصول على حساب بالمعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT * FROM accounts WHERE id = ?', (account_id,))
            result = cursor.fetchone()
            columns = [description[0] for description in cursor.description]
            return result, columns
        except Exception as e:
            return None, []
        finally:
            conn.close()
    
    def get_high_amount_accounts(self, threshold=1000):
        """الحصول على الحسابات ذات المبالغ العالية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT * FROM accounts 
                WHERE (OUTS + BKOUTS + OTHCHARGE) > ?
                ORDER BY (OUTS + BKOUTS + OTHCHARGE) DESC
            ''', (threshold,))
            
            results = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            return results, columns
            
        except Exception as e:
            return [], []
        finally:
            conn.close()
    
    def get_new_accounts(self, days=30):
        """الحصول على الحسابات الجديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT * FROM accounts 
                WHERE created_at >= datetime('now', '-{} days')
                ORDER BY created_at DESC
            '''.format(days))
            
            results = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            return results, columns
            
        except Exception as e:
            return [], []
        finally:
            conn.close()
