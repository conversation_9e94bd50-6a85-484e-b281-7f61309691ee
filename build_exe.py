# -*- coding: utf-8 -*-
"""
ملف إنشاء الملف التنفيذي
"""

import os
import sys
import subprocess

def install_requirements():
    """تثبيت المتطلبات"""
    print("تثبيت المتطلبات...")
    
    requirements = [
        'tkinter',
        'ttkthemes',
        'pillow',
        'reportlab',
        'openpyxl',
        'pyinstaller'
    ]
    
    for req in requirements:
        try:
            print(f"تثبيت {req}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
        except subprocess.CalledProcessError:
            print(f"فشل في تثبيت {req}")
        except Exception as e:
            print(f"خطأ في تثبيت {req}: {e}")

def create_executable():
    """إنشاء الملف التنفيذي"""
    print("إنشاء الملف التنفيذي...")
    
    # أوامر PyInstaller
    cmd = [
        'pyinstaller',
        '--onefile',                    # ملف واحد
        '--windowed',                   # بدون نافذة الكونسول
        '--name=ElectricityBills',      # اسم الملف التنفيذي
        '--icon=icon.ico',              # أيقونة (إذا كانت متوفرة)
        '--add-data=*.py;.',           # إضافة ملفات Python
        'main.py'                       # الملف الرئيسي
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("تم إنشاء الملف التنفيذي بنجاح!")
        print("يمكنك العثور على الملف في مجلد 'dist'")
    except subprocess.CalledProcessError as e:
        print(f"فشل في إنشاء الملف التنفيذي: {e}")
    except FileNotFoundError:
        print("PyInstaller غير مثبت. يرجى تثبيته أولاً:")
        print("pip install pyinstaller")

def create_spec_file():
    """إنشاء ملف spec مخصص"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.py', '.'),
        ('electricity_bills.db', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'ttkthemes',
        'PIL',
        'reportlab',
        'openpyxl',
        'sqlite3',
        'datetime',
        'csv',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ElectricityBills',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('ElectricityBills.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف ElectricityBills.spec")

def build_with_spec():
    """بناء الملف التنفيذي باستخدام ملف spec"""
    try:
        subprocess.run(['pyinstaller', 'ElectricityBills.spec'], check=True)
        print("تم إنشاء الملف التنفيذي بنجاح باستخدام ملف spec!")
    except subprocess.CalledProcessError as e:
        print(f"فشل في البناء: {e}")

def main():
    """الدالة الرئيسية"""
    print("=== إنشاء ملف تنفيذي لنظام إدارة فواتير الكهرباء ===")
    
    choice = input("""
اختر العملية المطلوبة:
1. تثبيت المتطلبات
2. إنشاء ملف تنفيذي بسيط
3. إنشاء ملف spec مخصص
4. بناء باستخدام ملف spec
5. تنفيذ جميع الخطوات

اختر رقم (1-5): """)
    
    if choice == '1':
        install_requirements()
    elif choice == '2':
        create_executable()
    elif choice == '3':
        create_spec_file()
    elif choice == '4':
        build_with_spec()
    elif choice == '5':
        install_requirements()
        create_spec_file()
        build_with_spec()
    else:
        print("اختيار غير صحيح")

if __name__ == "__main__":
    main()
