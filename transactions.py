# -*- coding: utf-8 -*-
"""
وحدة إدارة المعاملات
"""

import tkinter as tk
from tkinter import ttk
from database import DatabaseManager
from ui_components import CustomWidgets, MessageUtils, ValidationUtils
from themes import ThemeManager

class TransactionsWindow:
    def __init__(self, parent, theme_manager):
        self.parent = parent
        self.theme_manager = theme_manager
        self.db_manager = DatabaseManager()
        
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المعاملات")
        self.window.geometry("1200x800")
        self.window.configure(bg='white')
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_accounts()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة معاملات فواتير الكهرباء", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار العلوي
        top_buttons_frame = ttk.Frame(main_frame)
        top_buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        buttons_config = [
            ("إضافة حساب جديد", self.add_account, 'Success.TButton'),
            ("تعديل الحساب", self.edit_account, 'Primary.TButton'),
            ("حذف الحساب", self.delete_account, 'Danger.TButton'),
            ("تحديث القائمة", self.load_accounts, 'Warning.TButton')
        ]
        
        self.top_buttons_frame = CustomWidgets.create_button_frame(top_buttons_frame, buttons_config)
        self.top_buttons_frame.pack(side=tk.LEFT)
        
        # إطار البحث السريع
        search_frame = ttk.Frame(top_buttons_frame)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="بحث سريع:", style='Body.TLabel').pack(side=tk.LEFT, padx=(0, 5))
        self.quick_search_var = tk.StringVar()
        self.quick_search_var.trace('w', self.quick_search)
        ttk.Entry(search_frame, textvariable=self.quick_search_var, width=20).pack(side=tk.LEFT)
        
        # إطار الجدول
        table_frame = ttk.LabelFrame(main_frame, text="قائمة الحسابات")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # تحديد أعمدة الجدول
        columns = ['id', 'ACCTNO', 'NAME_A', 'MATER_NO', 'ADRESS', 'OUTS', 'BKOUTS', 'BILL_DATE']
        headings = ['المعرف', 'رقم الحساب', 'الاسم', 'رقم المقياس', 'العنوان', 'المستحق', 'المتأخر', 'تاريخ الفاتورة']
        
        self.tree_frame, self.accounts_tree = CustomWidgets.create_data_treeview(
            table_frame, columns, headings)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # ربط النقر المزدوج للتعديل
        self.accounts_tree.bind('<Double-1>', lambda e: self.edit_account())
        
        # إطار الأزرار السفلي
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(bottom_frame, text="إغلاق", 
                  command=self.window.destroy, 
                  style='Danger.TButton').pack(side=tk.RIGHT)
        
        # شريط الحالة
        self.status_frame, self.status_label, self.progress = CustomWidgets.create_status_bar(main_frame)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # متغيرات
        self.all_accounts = []
    
    def load_accounts(self):
        """تحميل جميع الحسابات"""
        try:
            self.progress.start()
            self.status_label.config(text="جاري تحميل الحسابات...")
            self.window.update()
            
            success, accounts, columns = self.db_manager.get_all_accounts()
            
            if success:
                self.all_accounts = accounts
                self.display_accounts(accounts, columns)
                self.status_label.config(text=f"تم تحميل {len(accounts)} حساب")
            else:
                MessageUtils.show_error("خطأ", accounts)
                self.status_label.config(text="فشل في تحميل الحسابات")
                
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في تحميل الحسابات: {str(e)}")
            self.status_label.config(text="خطأ في التحميل")
        finally:
            self.progress.stop()
    
    def display_accounts(self, accounts, columns):
        """عرض الحسابات في الجدول"""
        # مسح البيانات السابقة
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # إضافة البيانات الجديدة
        for row in accounts:
            display_data = []
            for col in ['id', 'ACCTNO', 'NAME_A', 'MATER_NO', 'ADRESS', 'OUTS', 'BKOUTS', 'BILL_DATE']:
                try:
                    col_index = columns.index(col)
                    display_data.append(row[col_index] if row[col_index] is not None else "")
                except (ValueError, IndexError):
                    display_data.append("")
            
            self.accounts_tree.insert('', tk.END, values=display_data)
    
    def quick_search(self, *args):
        """البحث السريع"""
        search_term = self.quick_search_var.get().lower()
        
        if not search_term:
            # إذا كان البحث فارغاً، عرض جميع الحسابات
            if hasattr(self, 'all_accounts'):
                success, accounts, columns = self.db_manager.get_all_accounts()
                if success:
                    self.display_accounts(accounts, columns)
            return
        
        # تصفية الحسابات
        try:
            search_criteria = {
                'name': search_term,
                'account_no': search_term,
                'meter_no': search_term
            }
            
            success, results, columns = self.db_manager.search_accounts(search_criteria)
            
            if success:
                self.display_accounts(results, columns)
                self.status_label.config(text=f"تم العثور على {len(results)} نتيجة")
            
        except Exception as e:
            print(f"خطأ في البحث السريع: {e}")
    
    def add_account(self):
        """إضافة حساب جديد"""
        AccountFormWindow(self.window, self.theme_manager, self.db_manager, 
                         mode='add', callback=self.load_accounts)
    
    def edit_account(self):
        """تعديل حساب"""
        selection = self.accounts_tree.selection()
        if not selection:
            MessageUtils.show_warning("تحذير", "يرجى اختيار حساب للتعديل")
            return
        
        item = self.accounts_tree.item(selection[0])
        account_id = item['values'][0]  # المعرف في العمود الأول
        
        AccountFormWindow(self.window, self.theme_manager, self.db_manager, 
                         mode='edit', account_id=account_id, callback=self.load_accounts)
    
    def delete_account(self):
        """حذف حساب"""
        selection = self.accounts_tree.selection()
        if not selection:
            MessageUtils.show_warning("تحذير", "يرجى اختيار حساب للحذف")
            return
        
        item = self.accounts_tree.item(selection[0])
        account_id = item['values'][0]
        account_name = item['values'][2]  # الاسم في العمود الثالث
        
        # طلب التأكيد
        if MessageUtils.ask_confirmation("تأكيد الحذف", 
                                       f"هل أنت متأكد من حذف الحساب: {account_name}؟\n\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                success, message = self.db_manager.delete_account(account_id)
                
                if success:
                    MessageUtils.show_success("نجح الحذف", message)
                    self.load_accounts()  # إعادة تحميل القائمة
                else:
                    MessageUtils.show_error("فشل الحذف", message)
                    
            except Exception as e:
                MessageUtils.show_error("خطأ", f"فشل في حذف الحساب: {str(e)}")
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

class AccountFormWindow:
    def __init__(self, parent, theme_manager, db_manager, mode='add', account_id=None, callback=None):
        self.parent = parent
        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.mode = mode  # 'add' أو 'edit'
        self.account_id = account_id
        self.callback = callback
        
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة حساب جديد" if mode == 'add' else "تعديل الحساب")
        self.window.geometry("800x700")
        self.window.configure(bg='white')
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        
        if mode == 'edit' and account_id:
            self.load_account_data()
        
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة النموذج"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_text = "إضافة حساب جديد" if self.mode == 'add' else "تعديل بيانات الحساب"
        title_label = ttk.Label(main_frame, text=title_text, style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(main_frame, bg='white')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # إنشاء حقول النموذج
        self.create_form_fields()
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        save_text = "إضافة" if self.mode == 'add' else "حفظ التغييرات"
        ttk.Button(button_frame, text=save_text, 
                  command=self.save_account, 
                  style='Success.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(button_frame, text="إلغاء", 
                  command=self.window.destroy, 
                  style='Danger.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        
        if self.mode == 'edit':
            ttk.Button(button_frame, text="مسح الحقول", 
                      command=self.clear_fields, 
                      style='Warning.TButton').pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_form_fields(self):
        """إنشاء حقول النموذج"""
        self.form_vars = {}
        
        # تعريف الحقول
        fields = [
            ('ACCTNO', 'رقم الحساب *', True),
            ('INSTALL_NO', 'رقم التركيب', False),
            ('SERIAL', 'الرقم التسلسلي', False),
            ('NAME_A', 'الاسم *', True),
            ('HOUSE_NO', 'رقم المنزل', False),
            ('ADRESS', 'العنوان', False),
            ('MATER_NO', 'رقم المقياس', False),
            ('MPHASE', 'نوع المقياس', False),
            ('METER_FACT', 'معامل المقياس', False),
            ('LAST_READ', 'القراءة الأخيرة', False),
            ('LAST_DATE', 'تاريخ القراءة الأخيرة', False),
            ('PREV_READ', 'القراءة السابقة', False),
            ('PREV_DATE', 'تاريخ القراءة السابقة', False),
            ('METER_RENT', 'إيجار المقياس', False),
            ('CB_RENT', 'إيجار القاطع', False),
            ('OTHCHARGE', 'رسوم أخرى', False),
            ('OUTS', 'المستحق', False),
            ('BKOUTS', 'المتأخر', False),
            ('HOUSE_COD', 'كود المنزل', False),
            ('EVEN_CLOSE', 'إغلاق زوجي', False),
            ('PAYMENT', 'الدفع', False),
            ('PAY_DATE', 'تاريخ الدفع', False),
            ('BILL_DATE', 'تاريخ الفاتورة', False),
            ('OLD_EXCH', 'صرف قديم', False),
            ('BAD_M_FLAG', 'علامة مقياس سيء', False),
            ('SPERE_FLAG', 'علامة احتياطي', False),
            ('BILL_CAL', 'حساب الفاتورة', False),
            ('AV_CONS', 'متوسط الاستهلاك', False),
            ('OPR_FLG', 'علامة التشغيل', False)
        ]
        
        # إنشاء الحقول في شبكة
        row = 0
        for field_name, field_label, required in fields:
            self.form_vars[field_name] = tk.StringVar()
            
            # إنشاء التسمية والحقل
            label, entry = CustomWidgets.create_labeled_entry(
                self.scrollable_frame, field_label, row, 0, 2)
            
            entry.config(textvariable=self.form_vars[field_name])
            
            # تلوين الحقول المطلوبة
            if required:
                label.config(foreground='red')
            
            row += 1
        
        # تكوين الشبكة
        for i in range(3):
            self.scrollable_frame.columnconfigure(i, weight=1)
    
    def load_account_data(self):
        """تحميل بيانات الحساب للتعديل"""
        try:
            account_data, columns = self.db_manager.get_account_by_id(self.account_id)
            
            if not account_data:
                MessageUtils.show_error("خطأ", "لم يتم العثور على بيانات الحساب")
                self.window.destroy()
                return
            
            # ملء الحقول بالبيانات
            for field_name, var in self.form_vars.items():
                try:
                    col_index = columns.index(field_name)
                    value = account_data[col_index] if account_data[col_index] is not None else ""
                    var.set(str(value))
                except (ValueError, IndexError):
                    var.set("")
                    
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في تحميل بيانات الحساب: {str(e)}")
            self.window.destroy()
    
    def save_account(self):
        """حفظ الحساب"""
        try:
            # جمع البيانات من النموذج
            account_data = {}
            for field_name, var in self.form_vars.items():
                value = var.get().strip()
                if value:
                    account_data[field_name] = value
            
            # التحقق من صحة البيانات
            is_valid, errors = ValidationUtils.validate_form_data(account_data)
            
            if not is_valid:
                MessageUtils.show_validation_errors(errors)
                return
            
            # حفظ البيانات
            if self.mode == 'add':
                success, result = self.db_manager.add_account(account_data)
                success_message = "تم إضافة الحساب بنجاح"
            else:
                success, result = self.db_manager.update_account(self.account_id, account_data)
                success_message = "تم تحديث الحساب بنجاح"
            
            if success:
                MessageUtils.show_success("نجح", success_message)
                if self.callback:
                    self.callback()  # تحديث القائمة الرئيسية
                self.window.destroy()
            else:
                MessageUtils.show_error("فشل", result)
                
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في حفظ الحساب: {str(e)}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        for var in self.form_vars.values():
            var.set("")
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
