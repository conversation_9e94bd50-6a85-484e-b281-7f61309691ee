@echo off
echo ========================================
echo    تثبيت متطلبات نظام إدارة فواتير الكهرباء
echo ========================================
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

echo تم العثور على Python
python --version
echo.

echo جاري تثبيت المتطلبات...
echo.

REM تحديث pip
echo تحديث pip...
python -m pip install --upgrade pip

REM تثبيت المتطلبات الأساسية
echo.
echo تثبيت المتطلبات الأساسية...
pip install ttkthemes
pip install pillow
pip install reportlab
pip install openpyxl
pip install pandas
pip install matplotlib

echo.
echo ========================================
echo تم تثبيت جميع المتطلبات بنجاح!
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo run_app.bat
echo أو
echo python main.py
echo ========================================
echo.
pause
