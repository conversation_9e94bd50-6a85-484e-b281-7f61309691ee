# -*- coding: utf-8 -*-
"""
وحدة التقارير
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from datetime import datetime
import csv
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

from database import DatabaseManager
from ui_components import MessageUtils, FileUtils
from config import REPORT_SETTINGS

class ReportGenerator:
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def export_search_results(self, data, format_type='Excel'):
        """تصدير نتائج البحث"""
        if not data:
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type == 'Excel' and EXCEL_AVAILABLE:
            filename = f"search_results_{timestamp}.xlsx"
            return self.export_to_excel(data, filename, "نتائج البحث")
        
        elif format_type == 'PDF' and PDF_AVAILABLE:
            filename = f"search_results_{timestamp}.pdf"
            return self.export_to_pdf(data, filename, "نتائج البحث")
        
        else:  # CSV as fallback
            filename = f"search_results_{timestamp}.csv"
            return self.export_to_csv(data, filename)
    
    def export_to_excel(self, data, filename, sheet_name):
        """تصدير إلى Excel"""
        try:
            # إنشاء ملف Excel جديد
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = sheet_name
            
            # العناوين
            headers = ['المعرف', 'رقم الحساب', 'الاسم', 'رقم المقياس', 'العنوان', 'المستحق', 'المتأخر', 'تاريخ الفاتورة']
            
            # إضافة العناوين
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # إضافة البيانات
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx, value=value)
                    cell.alignment = Alignment(horizontal="center")
            
            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف
            filepath = os.path.join(os.getcwd(), filename)
            workbook.save(filepath)
            return filepath
            
        except Exception as e:
            MessageUtils.show_error("خطأ في التصدير", f"فشل في تصدير Excel: {str(e)}")
            return None
    
    def export_to_pdf(self, data, filename, title):
        """تصدير إلى PDF"""
        try:
            filepath = os.path.join(os.getcwd(), filename)
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            
            # إعداد الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # وسط
            )
            
            # العناصر
            elements = []
            
            # العنوان
            title_para = Paragraph(title, title_style)
            elements.append(title_para)
            elements.append(Spacer(1, 20))
            
            # إعداد الجدول
            headers = ['ID', 'Account No', 'Name', 'Meter No', 'Address', 'Outstanding', 'Overdue', 'Bill Date']
            table_data = [headers] + data
            
            # إنشاء الجدول
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            elements.append(table)
            
            # بناء PDF
            doc.build(elements)
            return filepath
            
        except Exception as e:
            MessageUtils.show_error("خطأ في التصدير", f"فشل في تصدير PDF: {str(e)}")
            return None
    
    def export_to_csv(self, data, filename):
        """تصدير إلى CSV"""
        try:
            filepath = os.path.join(os.getcwd(), filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # العناوين
                headers = ['المعرف', 'رقم الحساب', 'الاسم', 'رقم المقياس', 'العنوان', 'المستحق', 'المتأخر', 'تاريخ الفاتورة']
                writer.writerow(headers)
                
                # البيانات
                for row in data:
                    writer.writerow(row)
            
            return filepath
            
        except Exception as e:
            MessageUtils.show_error("خطأ في التصدير", f"فشل في تصدير CSV: {str(e)}")
            return None

class ReportsWindow:
    def __init__(self, parent, theme_manager):
        self.parent = parent
        self.theme_manager = theme_manager
        self.db_manager = DatabaseManager()
        self.report_generator = ReportGenerator()
        
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير")
        self.window.geometry("900x600")
        self.window.configure(bg='white')
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="تقارير فواتير الكهرباء", style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # إطار أنواع التقارير
        reports_frame = ttk.LabelFrame(main_frame, text="أنواع التقارير")
        reports_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # تقرير المبالغ العالية
        high_amount_frame = ttk.Frame(reports_frame)
        high_amount_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(high_amount_frame, text="تقرير المبالغ العالية:", style='Header.TLabel').pack(side=tk.LEFT)
        
        self.high_amount_var = tk.StringVar(value=str(REPORT_SETTINGS['high_amount_threshold']))
        ttk.Label(high_amount_frame, text="الحد الأدنى:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Entry(high_amount_frame, textvariable=self.high_amount_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(high_amount_frame, text="إنشاء التقرير", 
                  command=self.generate_high_amount_report, 
                  style='Primary.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(high_amount_frame, text="تصدير", 
                  command=lambda: self.export_report('high_amount'), 
                  style='Success.TButton').pack(side=tk.LEFT, padx=5)
        
        # تقرير الحسابات الجديدة
        new_accounts_frame = ttk.Frame(reports_frame)
        new_accounts_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(new_accounts_frame, text="تقرير الحسابات الجديدة:", style='Header.TLabel').pack(side=tk.LEFT)
        
        self.new_accounts_days_var = tk.StringVar(value="30")
        ttk.Label(new_accounts_frame, text="آخر (أيام):").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Entry(new_accounts_frame, textvariable=self.new_accounts_days_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(new_accounts_frame, text="إنشاء التقرير", 
                  command=self.generate_new_accounts_report, 
                  style='Primary.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(new_accounts_frame, text="تصدير", 
                  command=lambda: self.export_report('new_accounts'), 
                  style='Success.TButton').pack(side=tk.LEFT, padx=5)
        
        # تقرير جميع الحسابات
        all_accounts_frame = ttk.Frame(reports_frame)
        all_accounts_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(all_accounts_frame, text="تقرير جميع الحسابات:", style='Header.TLabel').pack(side=tk.LEFT)
        
        ttk.Button(all_accounts_frame, text="إنشاء التقرير", 
                  command=self.generate_all_accounts_report, 
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(20, 5))
        
        ttk.Button(all_accounts_frame, text="تصدير", 
                  command=lambda: self.export_report('all_accounts'), 
                  style='Success.TButton').pack(side=tk.LEFT, padx=5)
        
        # إطار عرض النتائج
        results_frame = ttk.LabelFrame(main_frame, text="نتائج التقرير")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # الجدول
        columns = ['ACCTNO', 'NAME_A', 'MATER_NO', 'OUTS', 'BKOUTS', 'BILL_DATE']
        headings = ['رقم الحساب', 'الاسم', 'رقم المقياس', 'المستحق', 'المتأخر', 'تاريخ الفاتورة']
        
        from ui_components import CustomWidgets
        self.tree_frame, self.results_tree = CustomWidgets.create_data_treeview(
            results_frame, columns, headings)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إطار الأزرار السفلي
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(bottom_frame, text="إغلاق", 
                  command=self.window.destroy, 
                  style='Danger.TButton').pack(side=tk.RIGHT)
        
        # متغيرات لحفظ البيانات الحالية
        self.current_data = []
        self.current_report_type = None
    
    def generate_high_amount_report(self):
        """إنشاء تقرير المبالغ العالية"""
        try:
            threshold = float(self.high_amount_var.get())
            accounts, columns = self.db_manager.get_high_amount_accounts(threshold)
            
            self.display_report_results(accounts, columns)
            self.current_report_type = 'high_amount'
            
            MessageUtils.show_success("نجح", f"تم إنشاء تقرير المبالغ العالية - {len(accounts)} حساب")
            
        except ValueError:
            MessageUtils.show_error("خطأ", "يرجى إدخال رقم صحيح للحد الأدنى")
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def generate_new_accounts_report(self):
        """إنشاء تقرير الحسابات الجديدة"""
        try:
            days = int(self.new_accounts_days_var.get())
            accounts, columns = self.db_manager.get_new_accounts(days)
            
            self.display_report_results(accounts, columns)
            self.current_report_type = 'new_accounts'
            
            MessageUtils.show_success("نجح", f"تم إنشاء تقرير الحسابات الجديدة - {len(accounts)} حساب")
            
        except ValueError:
            MessageUtils.show_error("خطأ", "يرجى إدخال رقم صحيح للأيام")
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def generate_all_accounts_report(self):
        """إنشاء تقرير جميع الحسابات"""
        try:
            success, accounts, columns = self.db_manager.get_all_accounts()
            
            if success:
                self.display_report_results(accounts, columns)
                self.current_report_type = 'all_accounts'
                MessageUtils.show_success("نجح", f"تم إنشاء تقرير جميع الحسابات - {len(accounts)} حساب")
            else:
                MessageUtils.show_error("خطأ", accounts)
                
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def display_report_results(self, accounts, columns):
        """عرض نتائج التقرير"""
        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # حفظ البيانات الحالية
        self.current_data = []
        
        # إضافة النتائج الجديدة
        for row in accounts:
            # استخراج البيانات المطلوبة للعرض
            display_data = []
            for col in ['ACCTNO', 'NAME_A', 'MATER_NO', 'OUTS', 'BKOUTS', 'BILL_DATE']:
                try:
                    col_index = columns.index(col)
                    value = row[col_index] if row[col_index] is not None else ""
                    display_data.append(value)
                except (ValueError, IndexError):
                    display_data.append("")
            
            self.results_tree.insert('', tk.END, values=display_data)
            self.current_data.append(display_data)
    
    def export_report(self, report_type):
        """تصدير التقرير"""
        if not self.current_data:
            MessageUtils.show_warning("تحذير", "لا توجد بيانات للتصدير")
            return
        
        try:
            # اختيار نوع التصدير
            export_window = tk.Toplevel(self.window)
            export_window.title("تصدير التقرير")
            export_window.geometry("300x200")
            export_window.transient(self.window)
            export_window.grab_set()
            
            ttk.Label(export_window, text="اختر نوع التصدير:", style='Header.TLabel').pack(pady=20)
            
            format_var = tk.StringVar(value="Excel")
            
            formats = []
            if EXCEL_AVAILABLE:
                formats.append("Excel")
            if PDF_AVAILABLE:
                formats.append("PDF")
            formats.append("CSV")
            
            for fmt in formats:
                ttk.Radiobutton(export_window, text=fmt, variable=format_var, value=fmt).pack(pady=5)
            
            def do_export():
                filename = self.report_generator.export_search_results(self.current_data, format_var.get())
                if filename:
                    MessageUtils.show_success("نجح التصدير", f"تم تصدير التقرير إلى: {filename}")
                export_window.destroy()
            
            ttk.Button(export_window, text="تصدير", command=do_export, style='Primary.TButton').pack(pady=20)
            
        except Exception as e:
            MessageUtils.show_error("خطأ في التصدير", f"فشل في تصدير التقرير: {str(e)}")
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
