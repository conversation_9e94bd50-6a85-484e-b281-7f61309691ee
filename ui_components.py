# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import re

class CustomWidgets:
    """مكونات واجهة مخصصة"""
    
    @staticmethod
    def create_labeled_entry(parent, label_text, row, column=0, columnspan=1, sticky="ew", **kwargs):
        """إنشاء حقل إدخال مع تسمية"""
        label = ttk.Label(parent, text=label_text, style='Body.TLabel')
        label.grid(row=row, column=column, sticky="e", padx=(5, 2), pady=2)
        
        entry = ttk.Entry(parent, style='Custom.TEntry', **kwargs)
        entry.grid(row=row, column=column+1, columnspan=columnspan, sticky=sticky, padx=(2, 5), pady=2)
        
        return label, entry
    
    @staticmethod
    def create_labeled_combobox(parent, label_text, values, row, column=0, columnspan=1, sticky="ew", **kwargs):
        """إنشاء قائمة منسدلة مع تسمية"""
        label = ttk.Label(parent, text=label_text, style='Body.TLabel')
        label.grid(row=row, column=column, sticky="e", padx=(5, 2), pady=2)
        
        combobox = ttk.Combobox(parent, values=values, **kwargs)
        combobox.grid(row=row, column=column+1, columnspan=columnspan, sticky=sticky, padx=(2, 5), pady=2)
        
        return label, combobox
    
    @staticmethod
    def create_button_frame(parent, buttons_config):
        """إنشاء إطار للأزرار"""
        frame = ttk.Frame(parent)
        
        for i, (text, command, style) in enumerate(buttons_config):
            btn = ttk.Button(frame, text=text, command=command, style=style)
            btn.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
            frame.columnconfigure(i, weight=1)
        
        return frame
    
    @staticmethod
    def create_search_frame(parent, search_callback):
        """إنشاء إطار البحث"""
        search_frame = ttk.LabelFrame(parent, text="البحث المتقدم", style='Card.TFrame')
        
        # حقول البحث
        search_vars = {}
        
        # البحث بالاسم
        _, search_vars['name_entry'] = CustomWidgets.create_labeled_entry(
            search_frame, "الاسم:", 0, 0, 2)
        
        # البحث برقم الحساب
        _, search_vars['account_entry'] = CustomWidgets.create_labeled_entry(
            search_frame, "رقم الحساب:", 1, 0, 2)
        
        # البحث برقم المقياس
        _, search_vars['meter_entry'] = CustomWidgets.create_labeled_entry(
            search_frame, "رقم المقياس:", 2, 0, 2)
        
        # البحث بالتاريخ من
        _, search_vars['date_from_entry'] = CustomWidgets.create_labeled_entry(
            search_frame, "من تاريخ:", 0, 3, 2)
        
        # البحث بالتاريخ إلى
        _, search_vars['date_to_entry'] = CustomWidgets.create_labeled_entry(
            search_frame, "إلى تاريخ:", 1, 3, 2)
        
        # خيارات الترتيب
        _, search_vars['sort_by'] = CustomWidgets.create_labeled_combobox(
            search_frame, "ترتيب حسب:", 
            ["NAME_A", "ACCTNO", "MATER_NO", "BILL_DATE", "OUTS"], 2, 3)
        search_vars['sort_by'].set("NAME_A")
        
        # اتجاه الترتيب
        _, search_vars['sort_order'] = CustomWidgets.create_labeled_combobox(
            search_frame, "الاتجاه:", ["ASC", "DESC"], 3, 3)
        search_vars['sort_order'].set("ASC")
        
        # أزرار البحث
        button_frame = ttk.Frame(search_frame)
        button_frame.grid(row=4, column=0, columnspan=6, pady=10)
        
        search_btn = ttk.Button(button_frame, text="بحث", 
                               command=lambda: search_callback(search_vars),
                               style='Primary.TButton')
        search_btn.pack(side=tk.RIGHT, padx=5)
        
        clear_btn = ttk.Button(button_frame, text="مسح", 
                              command=lambda: CustomWidgets.clear_search_fields(search_vars),
                              style='Warning.TButton')
        clear_btn.pack(side=tk.RIGHT, padx=5)
        
        # تكوين الشبكة
        for i in range(6):
            search_frame.columnconfigure(i, weight=1)
        
        return search_frame, search_vars
    
    @staticmethod
    def clear_search_fields(search_vars):
        """مسح حقول البحث"""
        for key, widget in search_vars.items():
            if hasattr(widget, 'delete'):
                widget.delete(0, tk.END)
            elif hasattr(widget, 'set'):
                if key == 'sort_by':
                    widget.set("NAME_A")
                elif key == 'sort_order':
                    widget.set("ASC")
                else:
                    widget.set("")
    
    @staticmethod
    def create_data_treeview(parent, columns, headings):
        """إنشاء جدول البيانات"""
        # إطار الجدول مع شريط التمرير
        tree_frame = ttk.Frame(parent)
        
        # إنشاء الجدول
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', style='Custom.Treeview')
        
        # تكوين العناوين
        for col, heading in zip(columns, headings):
            tree.heading(col, text=heading, anchor='center')
            tree.column(col, width=100, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # تكوين الشبكة
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        return tree_frame, tree
    
    @staticmethod
    def create_status_bar(parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        
        # تسمية الحالة
        status_label = ttk.Label(status_frame, text="جاهز", style='Body.TLabel')
        status_label.pack(side=tk.LEFT, padx=5)
        
        # شريط التقدم
        progress = ttk.Progressbar(status_frame, style='Custom.TProgressbar', mode='indeterminate')
        progress.pack(side=tk.RIGHT, padx=5)
        
        return status_frame, status_label, progress

class ValidationUtils:
    """أدوات التحقق من صحة البيانات"""
    
    @staticmethod
    def validate_account_number(account_no):
        """التحقق من صحة رقم الحساب"""
        if not account_no or len(account_no.strip()) == 0:
            return False, "رقم الحساب مطلوب"
        
        if len(account_no) < 3:
            return False, "رقم الحساب يجب أن يكون 3 أرقام على الأقل"
        
        return True, ""
    
    @staticmethod
    def validate_name(name):
        """التحقق من صحة الاسم"""
        if not name or len(name.strip()) == 0:
            return False, "الاسم مطلوب"
        
        if len(name.strip()) < 2:
            return False, "الاسم يجب أن يكون حرفين على الأقل"
        
        return True, ""
    
    @staticmethod
    def validate_number(value, field_name, required=False):
        """التحقق من صحة الأرقام"""
        if not value or len(str(value).strip()) == 0:
            if required:
                return False, f"{field_name} مطلوب"
            return True, ""
        
        try:
            float(value)
            return True, ""
        except ValueError:
            return False, f"{field_name} يجب أن يكون رقماً صحيحاً"
    
    @staticmethod
    def validate_date(date_str, field_name):
        """التحقق من صحة التاريخ"""
        if not date_str or len(date_str.strip()) == 0:
            return True, ""
        
        # تنسيقات التاريخ المقبولة
        date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y']
        
        for fmt in date_formats:
            try:
                datetime.strptime(date_str, fmt)
                return True, ""
            except ValueError:
                continue
        
        return False, f"{field_name} تنسيق التاريخ غير صحيح (استخدم: YYYY-MM-DD أو DD/MM/YYYY)"
    
    @staticmethod
    def validate_form_data(data):
        """التحقق من صحة بيانات النموذج"""
        errors = []
        
        # التحقق من رقم الحساب
        is_valid, error = ValidationUtils.validate_account_number(data.get('ACCTNO', ''))
        if not is_valid:
            errors.append(error)
        
        # التحقق من الاسم
        is_valid, error = ValidationUtils.validate_name(data.get('NAME_A', ''))
        if not is_valid:
            errors.append(error)
        
        # التحقق من الأرقام
        numeric_fields = ['METER_FACT', 'LAST_READ', 'PREV_READ', 'METER_RENT', 
                         'CB_RENT', 'OTHCHARGE', 'OUTS', 'BKOUTS', 'PAYMENT', 
                         'BILL_CAL', 'AV_CONS']
        
        for field in numeric_fields:
            if field in data:
                is_valid, error = ValidationUtils.validate_number(data[field], field)
                if not is_valid:
                    errors.append(error)
        
        # التحقق من التواريخ
        date_fields = ['LAST_DATE', 'PREV_DATE', 'PAY_DATE', 'BILL_DATE']
        
        for field in date_fields:
            if field in data:
                is_valid, error = ValidationUtils.validate_date(data[field], field)
                if not is_valid:
                    errors.append(error)
        
        return len(errors) == 0, errors

class MessageUtils:
    """أدوات الرسائل"""

    @staticmethod
    def show_success(title, message):
        """عرض رسالة نجاح"""
        messagebox.showinfo(title, message)

    @staticmethod
    def show_error(title, message):
        """عرض رسالة خطأ"""
        messagebox.showerror(title, message)

    @staticmethod
    def show_warning(title, message):
        """عرض رسالة تحذير"""
        messagebox.showwarning(title, message)

    @staticmethod
    def ask_confirmation(title, message):
        """طلب تأكيد من المستخدم"""
        return messagebox.askyesno(title, message)

    @staticmethod
    def show_validation_errors(errors):
        """عرض أخطاء التحقق"""
        if errors:
            error_message = "الأخطاء التالية يجب تصحيحها:\n\n" + "\n".join(f"• {error}" for error in errors)
            MessageUtils.show_error("خطأ في البيانات", error_message)

class FileUtils:
    """أدوات التعامل مع الملفات"""

    @staticmethod
    def save_file_dialog(title="حفظ الملف", filetypes=[("All files", "*.*")]):
        """حوار حفظ الملف"""
        return filedialog.asksaveasfilename(title=title, filetypes=filetypes)

    @staticmethod
    def open_file_dialog(title="فتح الملف", filetypes=[("All files", "*.*")]):
        """حوار فتح الملف"""
        return filedialog.askopenfilename(title=title, filetypes=filetypes)
