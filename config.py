# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
"""

import os

# إعدادات قاعدة البيانات
DATABASE_NAME = "electricity_bills.db"
DATABASE_PATH = os.path.join(os.path.dirname(__file__), DATABASE_NAME)

# إعدادات واجهة المستخدم
WINDOW_TITLE = "نظام إدارة فواتير الكهرباء"
WINDOW_SIZE = "1200x800"
WINDOW_MIN_SIZE = (1000, 600)

# الثيمات المتاحة
AVAILABLE_THEMES = [
    "arc",
    "equilux", 
    "adapta",
    "breeze",
    "ubuntu",
    "clam",
    "alt",
    "default"
]

# إعدادات الألوان
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'success': '#F18F01',
    'danger': '#C73E1D',
    'warning': '#F4A261',
    'info': '#264653',
    'light': '#F8F9FA',
    'dark': '#212529',
    'background': '#FFFFFF',
    'text': '#000000'
}

# إعدادات الخطوط
FONTS = {
    'title': ('Arial', 16, 'bold'),
    'header': ('Arial', 12, 'bold'),
    'body': ('Arial', 10),
    'small': ('Arial', 8)
}

# إعدادات التقارير
REPORT_SETTINGS = {
    'high_amount_threshold': 1000,  # المبلغ العالي
    'items_per_page': 50,
    'export_formats': ['PDF', 'Excel', 'CSV']
}

# رسائل النظام
MESSAGES = {
    'success_add': "تم إضافة السجل بنجاح",
    'success_update': "تم تحديث السجل بنجاح", 
    'success_delete': "تم حذف السجل بنجاح",
    'error_database': "خطأ في قاعدة البيانات",
    'error_validation': "خطأ في التحقق من البيانات",
    'confirm_delete': "هل أنت متأكد من حذف هذا السجل؟",
    'no_data_found': "لا توجد بيانات",
    'search_completed': "تم البحث بنجاح"
}
