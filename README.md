# نظام إدارة فواتير الكهرباء

نظام شامل لإدارة فواتير الكهرباء مطور بلغة Python مع واجهة مستخدم احترافية وقاعدة بيانات متكاملة.

## المميزات الرئيسية

### 🔍 البحث المتقدم
- بحث بالاسم
- بحث برقم الحساب
- بحث برقم المقياس
- بحث بفترة زمنية محددة
- ترتيب النتائج تصاعدياً وتنازلياً
- عرض النتائج في نافذة منفصلة

### 📊 التقارير الشاملة
- تقرير المبالغ العالية
- تقرير الحسابات الجديدة
- تقرير جميع الحسابات
- تصدير التقارير بصيغ متعددة (Excel, PDF, CSV)

### ⚙️ إدارة المعاملات
- إضافة حسابات جديدة
- تعديل بيانات الحسابات
- حذف الحسابات
- عرض تفاصيل كاملة للحسابات
- تسجيل جميع المعاملات

### 🎨 واجهة مستخدم احترافية
- تصميم عصري وجميل
- دعم ثيمات متعددة
- واجهة باللغة العربية
- أزرار وصول سريع
- شريط حالة تفاعلي

### 🗄️ قاعدة بيانات متكاملة
- قاعدة بيانات SQLite محلية
- جميع الحقول المطلوبة مدعومة
- فهرسة للبحث السريع
- نسخ احتياطي تلقائي

## متطلبات النظام

- Windows 10 أو أحدث
- Python 3.8 أو أحدث (للتطوير)
- 100 MB مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### الطريقة الأولى: تشغيل الملف التنفيذي
1. قم بتحميل الملف التنفيذي `ElectricityBills.exe`
2. قم بتشغيل الملف مباشرة
3. لا حاجة لتثبيت Python أو أي متطلبات إضافية

### الطريقة الثانية: تشغيل من الكود المصدري
1. تأكد من تثبيت Python 3.8+
2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```
3. قم بتشغيل التطبيق:
```bash
python main.py
```

### إنشاء ملف تنفيذي جديد
```bash
python build_exe.py
```

## هيكل المشروع

```
electricity-bills-system/
├── main.py                 # الملف الرئيسي للتطبيق
├── database.py            # إدارة قاعدة البيانات
├── ui_components.py       # مكونات واجهة المستخدم
├── search_module.py       # وحدة البحث المتقدم
├── reports.py             # وحدة التقارير
├── transactions.py        # إدارة المعاملات
├── themes.py              # إدارة الثيمات
├── config.py              # إعدادات التطبيق
├── build_exe.py           # إنشاء الملف التنفيذي
├── requirements.txt       # متطلبات Python
└── README.md              # دليل المستخدم
```

## قاعدة البيانات

### جدول الحسابات الرئيسي (accounts)
يحتوي على جميع الحقول المطلوبة:

- `ACCTNO` - رقم الحساب
- `INSTALL_NO` - رقم التركيب
- `SERIAL` - الرقم التسلسلي
- `NAME_A` - الاسم
- `HOUSE_NO` - رقم المنزل
- `ADRESS` - العنوان
- `MATER_NO` - رقم المقياس
- `MPHASE` - نوع المقياس
- `METER_FACT` - معامل المقياس
- `LAST_READ` - القراءة الأخيرة
- `LAST_DATE` - تاريخ القراءة الأخيرة
- `PREV_READ` - القراءة السابقة
- `PREV_DATE` - تاريخ القراءة السابقة
- `METER_RENT` - إيجار المقياس
- `CB_RENT` - إيجار القاطع
- `OTHCHARGE` - رسوم أخرى
- `OUTS` - المستحق
- `BKOUTS` - المتأخر
- `HOUSE_COD` - كود المنزل
- `EVEN_CLOSE` - إغلاق زوجي
- `PAYMENT` - الدفع
- `PAY_DATE` - تاريخ الدفع
- `BILL_DATE` - تاريخ الفاتورة
- `OLD_EXCH` - صرف قديم
- `BAD_M_FLAG` - علامة مقياس سيء
- `SPERE_FLAG` - علامة احتياطي
- `BILL_CAL` - حساب الفاتورة
- `AV_CONS` - متوسط الاستهلاك
- `OPR_FLG` - علامة التشغيل

## كيفية الاستخدام

### البحث المتقدم
1. انقر على "بحث متقدم" من الشاشة الرئيسية
2. أدخل معايير البحث المطلوبة
3. اختر طريقة الترتيب
4. انقر على "بحث"
5. انقر نقراً مزدوجاً على أي نتيجة لعرض التفاصيل

### إضافة حساب جديد
1. انقر على "إضافة حساب جديد"
2. املأ البيانات المطلوبة (الحقول المميزة بـ * مطلوبة)
3. انقر على "إضافة"

### إنشاء التقارير
1. انقر على "التقارير" من الشاشة الرئيسية
2. اختر نوع التقرير المطلوب
3. حدد المعايير إذا لزم الأمر
4. انقر على "إنشاء التقرير"
5. يمكنك تصدير التقرير بصيغ مختلفة

### تغيير الثيم
1. من قائمة "إعدادات"
2. اختر "الثيمات"
3. اختر الثيم المطلوب

## الثيمات المتاحة

- Arc (افتراضي)
- Equilux
- Adapta
- Breeze
- Ubuntu
- Clam
- Alt
- Default

## التصدير والاستيراد

### تصدير البيانات
- Excel (.xlsx)
- PDF (.pdf)
- CSV (.csv)

### استيراد البيانات
- قيد التطوير

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع دليل المستخدم المدمج في البرنامج
- تحقق من ملف README
- تأكد من تحديث البرنامج لآخر إصدار

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية.
جميع الحقوق محفوظة © 2024

## التحديثات المستقبلية

- [ ] استيراد البيانات من Excel
- [ ] نسخ احتياطي تلقائي
- [ ] تقارير إضافية
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة أخرى

## المساهمة في التطوير

نرحب بالمساهمات في تطوير هذا المشروع:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

---

**ملاحظة**: هذا النظام مصمم خصيصاً لإدارة فواتير الكهرباء ويمكن تخصيصه حسب احتياجات المؤسسة.
