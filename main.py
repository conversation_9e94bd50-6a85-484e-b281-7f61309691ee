# -*- coding: utf-8 -*-
"""
نظام إدارة فواتير الكهرباء
الملف الرئيسي للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from database import DatabaseManager
from themes import ThemeManager, RTLSupport
from ui_components import CustomWidgets, MessageUtils
from search_module import SearchWindow
from reports import ReportsWindow
from transactions import TransactionsWindow

class ElectricityBillsApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.minsize(*WINDOW_MIN_SIZE)
        self.root.configure(bg=COLORS['background'])
        
        # إعداد الثيم
        self.theme_manager = ThemeManager(self.root)
        
        # إعداد قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # توسيط النافذة
        self.center_window()
        
        # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
        self.add_sample_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إنشاء القائمة الرئيسية
        self.create_menu()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_separator()
        file_menu.add_command(label="استيراد", command=self.import_data)
        file_menu.add_command(label="تصدير", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.quit_app)
        
        # قائمة البحث
        search_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="بحث", menu=search_menu)
        search_menu.add_command(label="بحث متقدم", command=self.open_search)
        search_menu.add_command(label="بحث سريع", command=self.quick_search)
        
        # قائمة المعاملات
        transactions_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="معاملات", menu=transactions_menu)
        transactions_menu.add_command(label="إدارة الحسابات", command=self.open_transactions)
        transactions_menu.add_command(label="إضافة حساب", command=self.add_account)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="تقارير", menu=reports_menu)
        reports_menu.add_command(label="تقارير شاملة", command=self.open_reports)
        reports_menu.add_command(label="المبالغ العالية", command=self.high_amount_report)
        reports_menu.add_command(label="الحسابات الجديدة", command=self.new_accounts_report)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="إعدادات", menu=settings_menu)
        
        # قائمة فرعية للثيمات
        theme_menu = tk.Menu(settings_menu, tearoff=0)
        settings_menu.add_cascade(label="الثيمات", menu=theme_menu)
        
        for theme in AVAILABLE_THEMES:
            theme_menu.add_command(label=theme, command=lambda t=theme: self.change_theme(t))
        
        settings_menu.add_separator()
        settings_menu.add_command(label="إعدادات عامة", command=self.open_settings)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(self.root)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # أزرار شريط الأدوات
        buttons_config = [
            ("بحث متقدم", self.open_search, 'Primary.TButton'),
            ("إدارة المعاملات", self.open_transactions, 'Success.TButton'),
            ("التقارير", self.open_reports, 'Warning.TButton'),
            ("إضافة حساب", self.add_account, 'Success.TButton')
        ]
        
        self.toolbar = CustomWidgets.create_button_frame(toolbar_frame, buttons_config)
        self.toolbar.pack(side=tk.LEFT)
        
        # معلومات سريعة
        info_frame = ttk.Frame(toolbar_frame)
        info_frame.pack(side=tk.RIGHT)
        
        self.accounts_count_label = ttk.Label(info_frame, text="عدد الحسابات: 0", style='Body.TLabel')
        self.accounts_count_label.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(info_frame, text="تحديث", command=self.update_stats, 
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=5)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # العنوان الترحيبي
        welcome_frame = ttk.Frame(main_frame)
        welcome_frame.pack(fill=tk.X, pady=(0, 20))
        
        welcome_label = ttk.Label(welcome_frame, 
                                 text="مرحباً بك في نظام إدارة فواتير الكهرباء", 
                                 style='Title.TLabel')
        welcome_label.pack()
        
        subtitle_label = ttk.Label(welcome_frame, 
                                  text="نظام شامل لإدارة وتتبع فواتير الكهرباء مع إمكانيات بحث وتقارير متقدمة", 
                                  style='Header.TLabel')
        subtitle_label.pack(pady=(5, 0))
        
        # إطار الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إنشاء بطاقات الإحصائيات
        self.create_stats_cards(stats_frame)
        
        # إطار الوصول السريع
        quick_access_frame = ttk.LabelFrame(main_frame, text="الوصول السريع")
        quick_access_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء أزرار الوصول السريع
        self.create_quick_access_buttons(quick_access_frame)
    
    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # بطاقة إجمالي الحسابات
        self.total_accounts_card = self.create_stat_card(cards_frame, "إجمالي الحسابات", "0", 0)
        
        # بطاقة المبالغ العالية
        self.high_amounts_card = self.create_stat_card(cards_frame, "مبالغ عالية", "0", 1)
        
        # بطاقة الحسابات الجديدة
        self.new_accounts_card = self.create_stat_card(cards_frame, "حسابات جديدة", "0", 2)
        
        # بطاقة إجمالي المستحقات
        self.total_outstanding_card = self.create_stat_card(cards_frame, "إجمالي المستحقات", "0", 3)
        
        # تكوين الشبكة
        for i in range(4):
            cards_frame.columnconfigure(i, weight=1)
    
    def create_stat_card(self, parent, title, value, column):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.Frame(parent, style='Card.TFrame')
        card_frame.grid(row=0, column=column, padx=5, pady=5, sticky="ew")
        
        title_label = ttk.Label(card_frame, text=title, style='Header.TLabel')
        title_label.pack(pady=(10, 5))
        
        value_label = ttk.Label(card_frame, text=value, style='Title.TLabel')
        value_label.pack(pady=(0, 10))
        
        return value_label
    
    def create_quick_access_buttons(self, parent):
        """إنشاء أزرار الوصول السريع"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))
        
        quick_buttons_row1 = [
            ("🔍 بحث متقدم", self.open_search, 'Primary.TButton'),
            ("📊 التقارير", self.open_reports, 'Warning.TButton'),
            ("⚙️ إدارة المعاملات", self.open_transactions, 'Success.TButton')
        ]
        
        for i, (text, command, style) in enumerate(quick_buttons_row1):
            btn = ttk.Button(row1_frame, text=text, command=command, style=style)
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 10))
        
        quick_buttons_row2 = [
            ("➕ إضافة حساب جديد", self.add_account, 'Success.TButton'),
            ("📈 المبالغ العالية", self.high_amount_report, 'Warning.TButton'),
            ("🆕 الحسابات الجديدة", self.new_accounts_report, 'Primary.TButton')
        ]
        
        for i, (text, command, style) in enumerate(quick_buttons_row2):
            btn = ttk.Button(row2_frame, text=text, command=command, style=style)
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame, self.status_label, self.progress = CustomWidgets.create_status_bar(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # إضافة معلومات إضافية لشريط الحالة
        time_label = ttk.Label(self.status_frame, text="", style='Body.TLabel')
        time_label.pack(side=tk.RIGHT, padx=5)
        
        # تحديث الوقت كل ثانية
        self.update_time(time_label)
    
    def update_time(self, time_label):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        time_label.config(text=current_time)
        self.root.after(1000, lambda: self.update_time(time_label))
    
    def center_window(self):
        """توسيط النافذة الرئيسية"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        try:
            # التحقق من وجود بيانات
            success, accounts, columns = self.db_manager.get_all_accounts()
            
            if success and len(accounts) == 0:
                # إضافة بيانات تجريبية
                sample_accounts = [
                    {
                        'ACCTNO': '001001',
                        'NAME_A': 'أحمد محمد علي',
                        'MATER_NO': 'M001',
                        'ADRESS': 'شارع الملك فهد، الرياض',
                        'OUTS': 1500.00,
                        'BKOUTS': 300.00,
                        'BILL_DATE': '2024-01-15'
                    },
                    {
                        'ACCTNO': '001002',
                        'NAME_A': 'فاطمة أحمد السالم',
                        'MATER_NO': 'M002',
                        'ADRESS': 'حي النخيل، جدة',
                        'OUTS': 2200.00,
                        'BKOUTS': 500.00,
                        'BILL_DATE': '2024-01-16'
                    },
                    {
                        'ACCTNO': '001003',
                        'NAME_A': 'محمد عبدالله الخالد',
                        'MATER_NO': 'M003',
                        'ADRESS': 'شارع الأمير سلطان، الدمام',
                        'OUTS': 800.00,
                        'BKOUTS': 0.00,
                        'BILL_DATE': '2024-01-17'
                    }
                ]
                
                for account in sample_accounts:
                    self.db_manager.add_account(account)
                
                self.status_label.config(text="تم إضافة بيانات تجريبية")
            
            # تحديث الإحصائيات
            self.update_stats()
            
        except Exception as e:
            print(f"خطأ في إضافة البيانات التجريبية: {e}")
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # إجمالي الحسابات
            success, accounts, columns = self.db_manager.get_all_accounts()
            if success:
                total_accounts = len(accounts)
                self.total_accounts_card.config(text=str(total_accounts))
                self.accounts_count_label.config(text=f"عدد الحسابات: {total_accounts}")
                
                # حساب إجمالي المستحقات
                total_outstanding = 0
                for account in accounts:
                    try:
                        outs_index = columns.index('OUTS')
                        bkouts_index = columns.index('BKOUTS')
                        outs = float(account[outs_index] or 0)
                        bkouts = float(account[bkouts_index] or 0)
                        total_outstanding += outs + bkouts
                    except (ValueError, IndexError):
                        continue
                
                self.total_outstanding_card.config(text=f"{total_outstanding:,.0f}")
            
            # المبالغ العالية
            high_accounts, _ = self.db_manager.get_high_amount_accounts()
            self.high_amounts_card.config(text=str(len(high_accounts)))
            
            # الحسابات الجديدة
            new_accounts, _ = self.db_manager.get_new_accounts()
            self.new_accounts_card.config(text=str(len(new_accounts)))
            
            self.status_label.config(text="تم تحديث الإحصائيات")
            
        except Exception as e:
            self.status_label.config(text=f"خطأ في تحديث الإحصائيات: {e}")
    
    # وظائف القوائم والأزرار
    def open_search(self):
        """فتح نافذة البحث المتقدم"""
        SearchWindow(self.root, self.theme_manager)
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        ReportsWindow(self.root, self.theme_manager)
    
    def open_transactions(self):
        """فتح نافذة إدارة المعاملات"""
        TransactionsWindow(self.root, self.theme_manager)
    
    def add_account(self):
        """إضافة حساب جديد"""
        from transactions import AccountFormWindow
        AccountFormWindow(self.root, self.theme_manager, self.db_manager, 
                         mode='add', callback=self.update_stats)
    
    def high_amount_report(self):
        """تقرير المبالغ العالية"""
        reports_window = ReportsWindow(self.root, self.theme_manager)
        # يمكن إضافة كود لفتح التقرير مباشرة
    
    def new_accounts_report(self):
        """تقرير الحسابات الجديدة"""
        reports_window = ReportsWindow(self.root, self.theme_manager)
        # يمكن إضافة كود لفتح التقرير مباشرة
    
    def change_theme(self, theme_name):
        """تغيير الثيم"""
        if self.theme_manager.apply_theme(theme_name):
            self.status_label.config(text=f"تم تطبيق الثيم: {theme_name}")
        else:
            self.status_label.config(text="فشل في تطبيق الثيم")
    
    def new_file(self):
        """ملف جديد"""
        MessageUtils.show_info("معلومات", "وظيفة ملف جديد قيد التطوير")
    
    def import_data(self):
        """استيراد البيانات"""
        MessageUtils.show_info("معلومات", "وظيفة استيراد البيانات قيد التطوير")
    
    def export_data(self):
        """تصدير البيانات"""
        MessageUtils.show_info("معلومات", "وظيفة تصدير البيانات قيد التطوير")
    
    def quick_search(self):
        """بحث سريع"""
        self.open_search()
    
    def open_settings(self):
        """فتح الإعدادات"""
        MessageUtils.show_info("معلومات", "نافذة الإعدادات قيد التطوير")
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        نظام إدارة فواتير الكهرباء
        
        الوظائف الرئيسية:
        • البحث المتقدم في الفواتير
        • إدارة الحسابات (إضافة، تعديل، حذف)
        • إنشاء التقارير المختلفة
        • تصدير البيانات بصيغ متعددة
        
        للمساعدة الإضافية، يرجى الاتصال بالدعم الفني.
        """
        MessageUtils.show_info("دليل المستخدم", help_text)
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        نظام إدارة فواتير الكهرباء
        الإصدار 1.0
        
        تم تطويره بلغة Python
        باستخدام مكتبة Tkinter
        
        © 2024 جميع الحقوق محفوظة
        """
        MessageUtils.show_info("حول البرنامج", about_text)
    
    def quit_app(self):
        """إغلاق التطبيق"""
        if MessageUtils.ask_confirmation("تأكيد الخروج", "هل أنت متأكد من إغلاق البرنامج؟"):
            self.root.quit()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = ElectricityBillsApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ فادح", f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
