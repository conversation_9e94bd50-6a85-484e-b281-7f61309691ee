# -*- coding: utf-8 -*-
"""
وحدة البحث المتقدم
"""

import tkinter as tk
from tkinter import ttk
from database import DatabaseManager
from ui_components import CustomWidgets, MessageUtils
from themes import ThemeManager

class SearchWindow:
    def __init__(self, parent, theme_manager):
        self.parent = parent
        self.theme_manager = theme_manager
        self.db_manager = DatabaseManager()
        
        self.window = tk.Toplevel(parent)
        self.window.title("البحث المتقدم")
        self.window.geometry("1000x700")
        self.window.configure(bg='white')
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="البحث المتقدم في فواتير الكهرباء", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إطار البحث
        self.search_frame, self.search_vars = CustomWidgets.create_search_frame(
            main_frame, self.perform_search)
        self.search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إطار النتائج
        results_frame = ttk.LabelFrame(main_frame, text="نتائج البحث")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # تحديد أعمدة الجدول
        columns = ['id', 'ACCTNO', 'NAME_A', 'MATER_NO', 'ADRESS', 'OUTS', 'BKOUTS', 'BILL_DATE']
        headings = ['المعرف', 'رقم الحساب', 'الاسم', 'رقم المقياس', 'العنوان', 'المستحق', 'المتأخر', 'تاريخ الفاتورة']
        
        self.tree_frame, self.results_tree = CustomWidgets.create_data_treeview(
            results_frame, columns, headings)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # ربط النقر المزدوج لعرض التفاصيل
        self.results_tree.bind('<Double-1>', self.show_details)
        
        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        buttons_config = [
            ("عرض التفاصيل", self.show_details, 'Primary.TButton'),
            ("تصدير النتائج", self.export_results, 'Success.TButton'),
            ("إغلاق", self.window.destroy, 'Danger.TButton')
        ]
        
        self.button_frame = CustomWidgets.create_button_frame(button_frame, buttons_config)
        self.button_frame.pack(side=tk.RIGHT)
        
        # شريط الحالة
        self.status_frame, self.status_label, self.progress = CustomWidgets.create_status_bar(main_frame)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def perform_search(self, search_vars):
        """تنفيذ البحث"""
        try:
            self.progress.start()
            self.status_label.config(text="جاري البحث...")
            self.window.update()
            
            # جمع معايير البحث
            search_criteria = {}
            
            name = search_vars['name_entry'].get().strip()
            if name:
                search_criteria['name'] = name
            
            account_no = search_vars['account_entry'].get().strip()
            if account_no:
                search_criteria['account_no'] = account_no
            
            meter_no = search_vars['meter_entry'].get().strip()
            if meter_no:
                search_criteria['meter_no'] = meter_no
            
            date_from = search_vars['date_from_entry'].get().strip()
            if date_from:
                search_criteria['date_from'] = date_from
            
            date_to = search_vars['date_to_entry'].get().strip()
            if date_to:
                search_criteria['date_to'] = date_to
            
            # معايير الترتيب
            sort_by = search_vars['sort_by'].get()
            sort_order = search_vars['sort_order'].get()
            
            # تنفيذ البحث
            success, results, columns = self.db_manager.search_accounts(
                search_criteria, sort_by, sort_order)
            
            if success:
                self.display_results(results, columns)
                self.status_label.config(text=f"تم العثور على {len(results)} نتيجة")
            else:
                MessageUtils.show_error("خطأ في البحث", results)
                self.status_label.config(text="فشل البحث")
            
        except Exception as e:
            MessageUtils.show_error("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
            self.status_label.config(text="خطأ في البحث")
        finally:
            self.progress.stop()
    
    def display_results(self, results, columns):
        """عرض نتائج البحث"""
        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج الجديدة
        for row in results:
            # عرض الأعمدة المحددة فقط
            display_data = []
            for col in ['id', 'ACCTNO', 'NAME_A', 'MATER_NO', 'ADRESS', 'OUTS', 'BKOUTS', 'BILL_DATE']:
                try:
                    col_index = columns.index(col)
                    display_data.append(row[col_index] if row[col_index] is not None else "")
                except (ValueError, IndexError):
                    display_data.append("")
            
            self.results_tree.insert('', tk.END, values=display_data)
    
    def show_details(self, event=None):
        """عرض تفاصيل السجل المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            MessageUtils.show_warning("تحذير", "يرجى اختيار سجل لعرض تفاصيله")
            return
        
        item = self.results_tree.item(selection[0])
        account_id = item['values'][0]  # المعرف في العمود الأول
        
        # فتح نافذة التفاصيل
        DetailsWindow(self.window, account_id, self.theme_manager, self.db_manager)
    
    def export_results(self):
        """تصدير النتائج"""
        try:
            from reports import ReportGenerator
            
            # الحصول على البيانات المعروضة
            data = []
            for item in self.results_tree.get_children():
                values = self.results_tree.item(item)['values']
                data.append(values)
            
            if not data:
                MessageUtils.show_warning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            # إنشاء التقرير
            report_gen = ReportGenerator()
            filename = report_gen.export_search_results(data)
            
            if filename:
                MessageUtils.show_success("نجح التصدير", f"تم تصدير النتائج إلى: {filename}")
            
        except Exception as e:
            MessageUtils.show_error("خطأ في التصدير", f"فشل في تصدير النتائج: {str(e)}")

class DetailsWindow:
    def __init__(self, parent, account_id, theme_manager, db_manager):
        self.parent = parent
        self.account_id = account_id
        self.theme_manager = theme_manager
        self.db_manager = db_manager
        
        self.window = tk.Toplevel(parent)
        self.window.title("تفاصيل الحساب")
        self.window.geometry("800x600")
        self.window.configure(bg='white')
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        self.load_and_display_data()
        self.center_window()
    
    def load_and_display_data(self):
        """تحميل وعرض بيانات الحساب"""
        try:
            account_data, columns = self.db_manager.get_account_by_id(self.account_id)
            
            if not account_data:
                MessageUtils.show_error("خطأ", "لم يتم العثور على بيانات الحساب")
                self.window.destroy()
                return
            
            self.setup_ui(account_data, columns)
            
        except Exception as e:
            MessageUtils.show_error("خطأ", f"فشل في تحميل بيانات الحساب: {str(e)}")
            self.window.destroy()
    
    def setup_ui(self, account_data, columns):
        """إعداد واجهة عرض التفاصيل"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_frame, text=f"تفاصيل الحساب: {account_data[columns.index('NAME_A')]}", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(main_frame, bg='white')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # عرض البيانات في شكل حقول
        self.create_detail_fields(scrollable_frame, account_data, columns)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        close_btn = ttk.Button(button_frame, text="إغلاق", 
                              command=self.window.destroy, style='Primary.TButton')
        close_btn.pack(side=tk.RIGHT)
    
    def create_detail_fields(self, parent, account_data, columns):
        """إنشاء حقول عرض التفاصيل"""
        # تعريف أسماء الحقول بالعربية
        field_names = {
            'ACCTNO': 'رقم الحساب',
            'INSTALL_NO': 'رقم التركيب',
            'SERIAL': 'الرقم التسلسلي',
            'NAME_A': 'الاسم',
            'HOUSE_NO': 'رقم المنزل',
            'ADRESS': 'العنوان',
            'MATER_NO': 'رقم المقياس',
            'MPHASE': 'نوع المقياس',
            'METER_FACT': 'معامل المقياس',
            'LAST_READ': 'القراءة الأخيرة',
            'LAST_DATE': 'تاريخ القراءة الأخيرة',
            'PREV_READ': 'القراءة السابقة',
            'PREV_DATE': 'تاريخ القراءة السابقة',
            'METER_RENT': 'إيجار المقياس',
            'CB_RENT': 'إيجار القاطع',
            'OTHCHARGE': 'رسوم أخرى',
            'OUTS': 'المستحق',
            'BKOUTS': 'المتأخر',
            'HOUSE_COD': 'كود المنزل',
            'EVEN_CLOSE': 'إغلاق زوجي',
            'PAYMENT': 'الدفع',
            'PAY_DATE': 'تاريخ الدفع',
            'BILL_DATE': 'تاريخ الفاتورة',
            'OLD_EXCH': 'صرف قديم',
            'BAD_M_FLAG': 'علامة مقياس سيء',
            'SPERE_FLAG': 'علامة احتياطي',
            'BILL_CAL': 'حساب الفاتورة',
            'AV_CONS': 'متوسط الاستهلاك',
            'OPR_FLG': 'علامة التشغيل'
        }
        
        row = 0
        for i, column in enumerate(columns):
            if column in ['id', 'created_at', 'updated_at']:
                continue  # تخطي الحقول الداخلية
            
            field_name = field_names.get(column, column)
            value = account_data[i] if account_data[i] is not None else ""
            
            # تسمية الحقل
            label = ttk.Label(parent, text=f"{field_name}:", style='Header.TLabel')
            label.grid(row=row, column=0, sticky="e", padx=(5, 10), pady=5)
            
            # قيمة الحقل
            value_label = ttk.Label(parent, text=str(value), style='Body.TLabel')
            value_label.grid(row=row, column=1, sticky="w", padx=(10, 5), pady=5)
            
            row += 1
        
        # تكوين الشبكة
        parent.columnconfigure(1, weight=1)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
