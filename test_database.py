# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات
"""

from database import DatabaseManager
import random
from datetime import datetime, timedelta

def test_database():
    """اختبار وظائف قاعدة البيانات"""
    print("=== اختبار قاعدة البيانات ===")
    
    # إنشاء مدير قاعدة البيانات
    db = DatabaseManager()
    
    # اختبار إضافة بيانات تجريبية
    print("\n1. إضافة بيانات تجريبية...")
    
    sample_accounts = [
        {
            'ACCTNO': '100001',
            'NAME_A': 'أحمد محمد الأحمد',
            'MATER_NO': 'M100001',
            'ADRESS': 'شارع الملك فهد، حي الملز، الرياض',
            'OUTS': 1250.50,
            'BKOUTS': 200.00,
            'BILL_DATE': '2024-01-15',
            'LAST_READ': 15420,
            'PREV_READ': 15200,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00
        },
        {
            'ACCTNO': '100002',
            'NAME_A': 'فاطمة عبدالله السالم',
            'MATER_NO': 'M100002',
            'ADRESS': 'شارع الأمير محمد، حي النخيل، جدة',
            'OUTS': 2100.75,
            'BKOUTS': 450.00,
            'BILL_DATE': '2024-01-16',
            'LAST_READ': 18750,
            'PREV_READ': 18500,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00
        },
        {
            'ACCTNO': '100003',
            'NAME_A': 'محمد سعد الخالد',
            'MATER_NO': 'M100003',
            'ADRESS': 'شارع الملك عبدالعزيز، حي الفيصلية، الدمام',
            'OUTS': 875.25,
            'BKOUTS': 0.00,
            'BILL_DATE': '2024-01-17',
            'LAST_READ': 12300,
            'PREV_READ': 12100,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00
        },
        {
            'ACCTNO': '100004',
            'NAME_A': 'نورا أحمد المطيري',
            'MATER_NO': 'M100004',
            'ADRESS': 'شارع التحلية، حي السليمانية، الرياض',
            'OUTS': 1650.00,
            'BKOUTS': 300.50,
            'BILL_DATE': '2024-01-18',
            'LAST_READ': 16800,
            'PREV_READ': 16550,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00
        },
        {
            'ACCTNO': '100005',
            'NAME_A': 'خالد عبدالرحمن العتيبي',
            'MATER_NO': 'M100005',
            'ADRESS': 'شارع الأمير سلطان، حي الروضة، جدة',
            'OUTS': 3200.80,
            'BKOUTS': 800.00,
            'BILL_DATE': '2024-01-19',
            'LAST_READ': 22100,
            'PREV_READ': 21750,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00
        }
    ]
    
    added_count = 0
    for account in sample_accounts:
        success, result = db.add_account(account)
        if success:
            added_count += 1
            print(f"✓ تم إضافة الحساب: {account['NAME_A']}")
        else:
            print(f"✗ فشل في إضافة الحساب: {account['NAME_A']} - {result}")
    
    print(f"\nتم إضافة {added_count} حساب من أصل {len(sample_accounts)}")
    
    # اختبار البحث
    print("\n2. اختبار البحث...")
    
    # البحث بالاسم
    success, results, columns = db.search_accounts({'name': 'أحمد'})
    if success:
        print(f"✓ البحث بالاسم 'أحمد': تم العثور على {len(results)} نتيجة")
    else:
        print(f"✗ فشل البحث بالاسم: {results}")
    
    # البحث برقم الحساب
    success, results, columns = db.search_accounts({'account_no': '100001'})
    if success:
        print(f"✓ البحث برقم الحساب '100001': تم العثور على {len(results)} نتيجة")
    else:
        print(f"✗ فشل البحث برقم الحساب: {results}")
    
    # اختبار الحصول على جميع الحسابات
    print("\n3. اختبار الحصول على جميع الحسابات...")
    success, all_accounts, columns = db.get_all_accounts()
    if success:
        print(f"✓ تم الحصول على {len(all_accounts)} حساب")
        
        # عرض بعض الإحصائيات
        total_outstanding = 0
        total_overdue = 0
        
        outs_index = columns.index('OUTS') if 'OUTS' in columns else -1
        bkouts_index = columns.index('BKOUTS') if 'BKOUTS' in columns else -1
        
        for account in all_accounts:
            if outs_index >= 0 and account[outs_index]:
                total_outstanding += float(account[outs_index])
            if bkouts_index >= 0 and account[bkouts_index]:
                total_overdue += float(account[bkouts_index])
        
        print(f"  - إجمالي المستحقات: {total_outstanding:,.2f}")
        print(f"  - إجمالي المتأخرات: {total_overdue:,.2f}")
    else:
        print(f"✗ فشل في الحصول على الحسابات: {all_accounts}")
    
    # اختبار تقرير المبالغ العالية
    print("\n4. اختبار تقرير المبالغ العالية...")
    high_accounts, columns = db.get_high_amount_accounts(1000)
    print(f"✓ الحسابات ذات المبالغ العالية (أكثر من 1000): {len(high_accounts)} حساب")
    
    # اختبار تقرير الحسابات الجديدة
    print("\n5. اختبار تقرير الحسابات الجديدة...")
    new_accounts, columns = db.get_new_accounts(30)
    print(f"✓ الحسابات الجديدة (آخر 30 يوم): {len(new_accounts)} حساب")
    
    print("\n=== انتهى اختبار قاعدة البيانات ===")

def generate_more_sample_data():
    """إنشاء المزيد من البيانات التجريبية"""
    print("\n=== إنشاء بيانات تجريبية إضافية ===")
    
    db = DatabaseManager()
    
    # أسماء تجريبية
    first_names = ['أحمد', 'محمد', 'عبدالله', 'سعد', 'خالد', 'فهد', 'عبدالعزيز', 'سلطان',
                   'فاطمة', 'عائشة', 'خديجة', 'مريم', 'نورا', 'سارة', 'هند', 'ريم']
    
    last_names = ['الأحمد', 'المحمد', 'السالم', 'الخالد', 'العتيبي', 'المطيري', 'القحطاني', 
                  'الغامدي', 'الزهراني', 'الشهري', 'العمري', 'الحربي']
    
    cities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة', 'الطائف', 'تبوك', 'أبها']
    
    streets = ['شارع الملك فهد', 'شارع الأمير محمد', 'شارع الملك عبدالعزيز', 'شارع التحلية',
               'شارع الأمير سلطان', 'شارع العليا', 'شارع الملك خالد']
    
    districts = ['حي الملز', 'حي النخيل', 'حي الفيصلية', 'حي السليمانية', 'حي الروضة',
                 'حي العليا', 'حي الورود', 'حي النرجس']
    
    added_count = 0
    
    for i in range(6, 51):  # إضافة 45 حساب إضافي
        account_no = f"10{i:04d}"
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        full_name = f"{first_name} {random.choice(['محمد', 'أحمد', 'عبدالله', 'سعد'])} {last_name}"
        
        street = random.choice(streets)
        district = random.choice(districts)
        city = random.choice(cities)
        address = f"{street}، {district}، {city}"
        
        # تواريخ عشوائية
        bill_date = datetime.now() - timedelta(days=random.randint(1, 90))
        
        # قراءات عشوائية
        prev_read = random.randint(10000, 25000)
        last_read = prev_read + random.randint(100, 500)
        
        # مبالغ عشوائية
        outs = round(random.uniform(500, 4000), 2)
        bkouts = round(random.uniform(0, 1000), 2) if random.random() > 0.3 else 0
        
        account_data = {
            'ACCTNO': account_no,
            'NAME_A': full_name,
            'MATER_NO': f"M{account_no}",
            'ADRESS': address,
            'OUTS': outs,
            'BKOUTS': bkouts,
            'BILL_DATE': bill_date.strftime('%Y-%m-%d'),
            'LAST_READ': last_read,
            'PREV_READ': prev_read,
            'METER_RENT': 50.00,
            'CB_RENT': 25.00,
            'OTHCHARGE': round(random.uniform(0, 100), 2),
            'PAYMENT': round(random.uniform(0, outs), 2) if random.random() > 0.5 else 0
        }
        
        success, result = db.add_account(account_data)
        if success:
            added_count += 1
            if added_count % 10 == 0:
                print(f"تم إضافة {added_count} حساب...")
        else:
            if "موجود مسبقاً" not in str(result):
                print(f"فشل في إضافة الحساب {account_no}: {result}")
    
    print(f"\n✓ تم إضافة {added_count} حساب إضافي")
    print("=== انتهى إنشاء البيانات التجريبية ===")

if __name__ == "__main__":
    print("اختبار نظام إدارة فواتير الكهرباء")
    print("=" * 50)
    
    choice = input("""
اختر العملية:
1. اختبار قاعدة البيانات الأساسي
2. إنشاء بيانات تجريبية إضافية
3. تنفيذ كلا الاختبارين

اختر رقم (1-3): """)
    
    if choice == '1':
        test_database()
    elif choice == '2':
        generate_more_sample_data()
    elif choice == '3':
        test_database()
        generate_more_sample_data()
    else:
        print("اختيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")
