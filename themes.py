# -*- coding: utf-8 -*-
"""
إدارة الثيمات والألوان
"""

import tkinter as tk
from tkinter import ttk
try:
    from tkinter.ttk import Style
    from ttkthemes import ThemedTk, ThemedStyle
    THEMES_AVAILABLE = True
except ImportError:
    THEMES_AVAILABLE = False

from config import COLORS, FONTS, AVAILABLE_THEMES

class ThemeManager:
    def __init__(self, root):
        self.root = root
        self.current_theme = "arc"
        
        if THEMES_AVAILABLE:
            self.style = ThemedStyle(root)
        else:
            self.style = ttk.Style()
        
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """إعداد الأنماط المخصصة"""
        
        # نمط العنوان الرئيسي
        self.style.configure('Title.TLabel',
                           font=FONTS['title'],
                           foreground=COLORS['primary'],
                           background=COLORS['background'])
        
        # نمط العناوين الفرعية
        self.style.configure('Header.TLabel',
                           font=FONTS['header'],
                           foreground=COLORS['secondary'],
                           background=COLORS['background'])
        
        # نمط النص العادي
        self.style.configure('Body.TLabel',
                           font=FONTS['body'],
                           foreground=COLORS['text'],
                           background=COLORS['background'])
        
        # نمط الأزرار الأساسية
        self.style.configure('Primary.TButton',
                           font=FONTS['body'],
                           foreground='white',
                           background=COLORS['primary'],
                           borderwidth=0,
                           focuscolor='none')
        
        self.style.map('Primary.TButton',
                      background=[('active', COLORS['info']),
                                ('pressed', COLORS['dark'])])
        
        # نمط أزرار النجاح
        self.style.configure('Success.TButton',
                           font=FONTS['body'],
                           foreground='white',
                           background=COLORS['success'],
                           borderwidth=0,
                           focuscolor='none')
        
        # نمط أزرار الخطر
        self.style.configure('Danger.TButton',
                           font=FONTS['body'],
                           foreground='white',
                           background=COLORS['danger'],
                           borderwidth=0,
                           focuscolor='none')
        
        # نمط أزرار التحذير
        self.style.configure('Warning.TButton',
                           font=FONTS['body'],
                           foreground='white',
                           background=COLORS['warning'],
                           borderwidth=0,
                           focuscolor='none')
        
        # نمط الجداول
        self.style.configure('Custom.Treeview',
                           font=FONTS['body'],
                           background=COLORS['light'],
                           foreground=COLORS['text'],
                           fieldbackground=COLORS['background'])
        
        self.style.configure('Custom.Treeview.Heading',
                           font=FONTS['header'],
                           background=COLORS['primary'],
                           foreground='white')
        
        # نمط حقول الإدخال
        self.style.configure('Custom.TEntry',
                           font=FONTS['body'],
                           fieldbackground=COLORS['background'],
                           borderwidth=1,
                           relief='solid')
        
        # نمط الإطارات
        self.style.configure('Card.TFrame',
                           background=COLORS['background'],
                           relief='raised',
                           borderwidth=1)
        
        # نمط شريط التقدم
        self.style.configure('Custom.TProgressbar',
                           background=COLORS['primary'],
                           troughcolor=COLORS['light'],
                           borderwidth=0,
                           lightcolor=COLORS['primary'],
                           darkcolor=COLORS['primary'])
    
    def apply_theme(self, theme_name):
        """تطبيق ثيم معين"""
        if THEMES_AVAILABLE and theme_name in AVAILABLE_THEMES:
            try:
                self.style.set_theme(theme_name)
                self.current_theme = theme_name
                self.setup_custom_styles()  # إعادة تطبيق الأنماط المخصصة
                return True
            except Exception as e:
                print(f"خطأ في تطبيق الثيم: {e}")
                return False
        return False
    
    def get_available_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        if THEMES_AVAILABLE:
            return self.style.theme_names()
        else:
            return ['default']
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.current_theme
    
    def create_themed_window(self, title, size="800x600"):
        """إنشاء نافذة بالثيم المحدد"""
        if THEMES_AVAILABLE:
            window = ThemedTk(theme=self.current_theme)
        else:
            window = tk.Tk()
        
        window.title(title)
        window.geometry(size)
        window.configure(bg=COLORS['background'])
        
        return window
    
    def style_widget(self, widget, style_name):
        """تطبيق نمط على عنصر واجهة"""
        if hasattr(widget, 'configure'):
            if style_name == 'title':
                widget.configure(font=FONTS['title'], 
                               fg=COLORS['primary'], 
                               bg=COLORS['background'])
            elif style_name == 'header':
                widget.configure(font=FONTS['header'], 
                               fg=COLORS['secondary'], 
                               bg=COLORS['background'])
            elif style_name == 'body':
                widget.configure(font=FONTS['body'], 
                               fg=COLORS['text'], 
                               bg=COLORS['background'])
            elif style_name == 'primary_button':
                widget.configure(font=FONTS['body'], 
                               fg='white', 
                               bg=COLORS['primary'],
                               activebackground=COLORS['info'],
                               relief='flat',
                               bd=0)
            elif style_name == 'success_button':
                widget.configure(font=FONTS['body'], 
                               fg='white', 
                               bg=COLORS['success'],
                               activebackground=COLORS['warning'],
                               relief='flat',
                               bd=0)
            elif style_name == 'danger_button':
                widget.configure(font=FONTS['body'], 
                               fg='white', 
                               bg=COLORS['danger'],
                               activebackground='#A02622',
                               relief='flat',
                               bd=0)

class RTLSupport:
    """دعم الكتابة من اليمين إلى اليسار"""
    
    @staticmethod
    def configure_rtl_text(widget):
        """تكوين النص للكتابة من اليمين لليسار"""
        if hasattr(widget, 'configure'):
            widget.configure(justify='right')
    
    @staticmethod
    def configure_rtl_entry(widget):
        """تكوين حقل الإدخال للكتابة من اليمين لليسار"""
        if hasattr(widget, 'configure'):
            widget.configure(justify='right')
    
    @staticmethod
    def configure_rtl_treeview(treeview):
        """تكوين الجدول للعرض من اليمين لليسار"""
        # تكوين محاذاة الأعمدة
        for col in treeview['columns']:
            treeview.heading(col, anchor='e')  # محاذاة العنوان لليمين
            treeview.column(col, anchor='e')   # محاذاة المحتوى لليمين
