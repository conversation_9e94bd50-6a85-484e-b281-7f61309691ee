@echo off
echo ========================================
echo    إنشاء ملف تنفيذي لنظام إدارة فواتير الكهرباء
echo ========================================
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    pause
    exit /b 1
)

echo تحقق من تثبيت PyInstaller...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo تثبيت PyInstaller...
    pip install pyinstaller
)

echo.
echo جاري إنشاء الملف التنفيذي...
echo هذه العملية قد تستغرق عدة دقائق...
echo.

REM إنشاء الملف التنفيذي
pyinstaller --onefile --windowed --name=ElectricityBills --add-data="*.py;." main.py

if errorlevel 1 (
    echo.
    echo فشل في إنشاء الملف التنفيذي
    echo يرجى التحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم إنشاء الملف التنفيذي بنجاح!
echo يمكنك العثور على الملف في:
echo %CD%\dist\ElectricityBills.exe
echo ========================================
echo.

REM فتح مجلد الملف التنفيذي
if exist "dist\ElectricityBills.exe" (
    echo فتح مجلد الملف التنفيذي...
    explorer dist
)

pause
